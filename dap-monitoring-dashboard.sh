#!/bin/bash

# GitLab DAP Comprehensive Monitoring Dashboard
# Real-time monitoring and analysis of DAP traces across all services

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m'

# Configuration
GITLAB_LOG="../gdk/gitlab/log/development.log"
AI_GATEWAY_LOG="../gdk/log/gitlab-ai-gateway/gateway_debug.log"
DUO_WORKFLOW_LOG="../gdk/log/duo-workflow-service/current"
VSCODE_LOG="logs/gitlab-ai-logs.log"

print_header() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                GitLab DAP Monitoring Dashboard               ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_menu() {
    echo -e "${BLUE}Available Commands:${NC}"
    echo "  ${GREEN}1${NC} - Real-time trace monitoring"
    echo "  ${GREEN}2${NC} - Correlation ID analysis"
    echo "  ${GREEN}3${NC} - Agent orchestration view"
    echo "  ${GREEN}4${NC} - Tool execution tracking"
    echo "  ${GREEN}5${NC} - Error analysis"
    echo "  ${GREEN}6${NC} - Performance metrics"
    echo "  ${GREEN}7${NC} - LangSmith trace links"
    echo "  ${GREEN}8${NC} - Service health check"
    echo "  ${GREEN}q${NC} - Quit"
    echo ""
}

check_dependencies() {
    local missing_deps=()
    
    # Check for required tools
    command -v jq >/dev/null 2>&1 || missing_deps+=("jq")
    command -v curl >/dev/null 2>&1 || missing_deps+=("curl")
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}Missing dependencies: ${missing_deps[*]}${NC}"
        echo "Install with: brew install ${missing_deps[*]}"
        exit 1
    fi
}

check_log_files() {
    local missing_logs=()
    
    [ ! -f "$GITLAB_LOG" ] && missing_logs+=("GitLab Rails")
    [ ! -f "$AI_GATEWAY_LOG" ] && missing_logs+=("AI Gateway")
    [ ! -f "$DUO_WORKFLOW_LOG" ] && missing_logs+=("Duo Workflow")
    
    if [ ${#missing_logs[@]} -gt 0 ]; then
        echo -e "${YELLOW}Warning: Missing log files for: ${missing_logs[*]}${NC}"
        echo "Make sure all services are running: gdk status"
        echo ""
    fi
}

real_time_monitoring() {
    print_header
    echo -e "${GREEN}🔴 Real-time DAP Trace Monitoring${NC}"
    echo "Press Ctrl+C to return to menu"
    echo ""
    
    tail -f "$GITLAB_LOG" "$AI_GATEWAY_LOG" "$DUO_WORKFLOW_LOG" 2>/dev/null | \
    while IFS= read -r line; do
        case "$line" in
            *"==> $GITLAB_LOG <=="*)
                echo -e "${GREEN}[GitLab Rails]${NC}"
                ;;
            *"==> $AI_GATEWAY_LOG <=="*)
                echo -e "${BLUE}[AI Gateway]${NC}"
                ;;
            *"==> $DUO_WORKFLOW_LOG <=="*)
                echo -e "${YELLOW}[Duo Workflow]${NC}"
                ;;
            *correlation_id*|*request_id*)
                echo -e "${CYAN}🔗 $line${NC}"
                ;;
            *workflow_id*)
                echo -e "${MAGENTA}🔄 $line${NC}"
                ;;
            *tool*|*Tool*)
                echo -e "${GREEN}🔧 $line${NC}"
                ;;
            *agent*|*Agent*)
                echo -e "${BLUE}🤖 $line${NC}"
                ;;
            *error*|*Error*|*ERROR*)
                echo -e "${RED}❌ $line${NC}"
                ;;
            *)
                echo "$line"
                ;;
        esac
    done
}

correlation_analysis() {
    print_header
    echo -e "${GREEN}🔗 Correlation ID Analysis${NC}"
    echo ""
    
    echo "Recent correlation IDs (last 100 lines):"
    echo ""
    
    # Extract correlation IDs from all logs
    {
        tail -n 100 "$GITLAB_LOG" 2>/dev/null | grep -o 'correlation_id[":=][^",:}]*' | sed 's/correlation_id[":=]//' | sed 's/^/GitLab: /'
        tail -n 100 "$AI_GATEWAY_LOG" 2>/dev/null | grep -o 'correlation_id[":=][^",:}]*' | sed 's/correlation_id[":=]//' | sed 's/^/AI Gateway: /'
        tail -n 100 "$DUO_WORKFLOW_LOG" 2>/dev/null | grep -o 'correlation_id[":=][^",:}]*' | sed 's/correlation_id[":=]//' | sed 's/^/Duo Workflow: /'
    } | sort | uniq -c | sort -nr | head -10
    
    echo ""
    read -p "Enter correlation ID to trace (or press Enter to continue): " correlation_id
    
    if [ -n "$correlation_id" ]; then
        echo ""
        echo -e "${CYAN}Tracing correlation ID: $correlation_id${NC}"
        echo ""
        
        echo -e "${GREEN}GitLab Rails:${NC}"
        grep "$correlation_id" "$GITLAB_LOG" 2>/dev/null | tail -5 || echo "No matches found"
        echo ""
        
        echo -e "${BLUE}AI Gateway:${NC}"
        grep "$correlation_id" "$AI_GATEWAY_LOG" 2>/dev/null | tail -5 || echo "No matches found"
        echo ""
        
        echo -e "${YELLOW}Duo Workflow:${NC}"
        grep "$correlation_id" "$DUO_WORKFLOW_LOG" 2>/dev/null | tail -5 || echo "No matches found"
        echo ""
    fi
    
    read -p "Press Enter to continue..."
}

agent_orchestration_view() {
    print_header
    echo -e "${GREEN}🤖 Agent Orchestration View${NC}"
    echo ""
    
    echo "Recent agent activities:"
    echo ""
    
    # Look for agent-related activities
    {
        tail -n 200 "$DUO_WORKFLOW_LOG" 2>/dev/null | grep -i "agent\|workflow\|tool\|planning" | tail -10
    } | while IFS= read -r line; do
        case "$line" in
            *planning*|*Planning*)
                echo -e "${BLUE}📋 $line${NC}"
                ;;
            *tool*|*Tool*)
                echo -e "${GREEN}🔧 $line${NC}"
                ;;
            *workflow*|*Workflow*)
                echo -e "${MAGENTA}🔄 $line${NC}"
                ;;
            *)
                echo -e "${CYAN}🤖 $line${NC}"
                ;;
        esac
    done
    
    echo ""
    read -p "Press Enter to continue..."
}

tool_execution_tracking() {
    print_header
    echo -e "${GREEN}🔧 Tool Execution Tracking${NC}"
    echo ""
    
    echo "Recent tool executions:"
    echo ""
    
    # Track tool executions
    tail -n 200 "$DUO_WORKFLOW_LOG" 2>/dev/null | grep -i "tool" | tail -10 | \
    while IFS= read -r line; do
        case "$line" in
            *executing*|*Executing*)
                echo -e "${YELLOW}⚙️ $line${NC}"
                ;;
            *completed*|*Completed*|*success*)
                echo -e "${GREEN}✅ $line${NC}"
                ;;
            *failed*|*Failed*|*error*)
                echo -e "${RED}❌ $line${NC}"
                ;;
            *)
                echo -e "${BLUE}🔧 $line${NC}"
                ;;
        esac
    done
    
    echo ""
    echo "Tool execution summary:"
    tail -n 500 "$DUO_WORKFLOW_LOG" 2>/dev/null | grep -i "tool" | \
    grep -o '[a-zA-Z_]*tool[a-zA-Z_]*' | sort | uniq -c | sort -nr | head -5
    
    echo ""
    read -p "Press Enter to continue..."
}

error_analysis() {
    print_header
    echo -e "${GREEN}❌ Error Analysis${NC}"
    echo ""
    
    echo "Recent errors across all services:"
    echo ""
    
    # Analyze errors from all services
    echo -e "${GREEN}GitLab Rails errors:${NC}"
    tail -n 200 "$GITLAB_LOG" 2>/dev/null | grep -i "error\|exception" | tail -3 | \
    while IFS= read -r line; do
        echo -e "${RED}  → $line${NC}"
    done
    echo ""
    
    echo -e "${BLUE}AI Gateway errors:${NC}"
    tail -n 200 "$AI_GATEWAY_LOG" 2>/dev/null | grep -i "error\|exception" | tail -3 | \
    while IFS= read -r line; do
        echo -e "${RED}  → $line${NC}"
    done
    echo ""
    
    echo -e "${YELLOW}Duo Workflow errors:${NC}"
    tail -n 200 "$DUO_WORKFLOW_LOG" 2>/dev/null | grep -i "error\|exception" | tail -3 | \
    while IFS= read -r line; do
        echo -e "${RED}  → $line${NC}"
    done
    echo ""
    
    read -p "Press Enter to continue..."
}

performance_metrics() {
    print_header
    echo -e "${GREEN}📊 Performance Metrics${NC}"
    echo ""
    
    echo "Workflow performance analysis:"
    echo ""
    
    # Extract performance metrics
    tail -n 500 "$DUO_WORKFLOW_LOG" 2>/dev/null | grep -i "duration\|seconds\|ms" | tail -5 | \
    while IFS= read -r line; do
        echo -e "${CYAN}⏱️ $line${NC}"
    done
    
    echo ""
    echo "Service response times (if available):"
    tail -n 200 "$AI_GATEWAY_LOG" 2>/dev/null | grep -i "response.*time\|duration" | tail -3
    
    echo ""
    read -p "Press Enter to continue..."
}

langsmith_links() {
    print_header
    echo -e "${GREEN}🔗 LangSmith Trace Links${NC}"
    echo ""
    
    echo "LangSmith project: duo-workflow-local-debug"
    echo "URL: https://smith.langchain.com/"
    echo ""
    
    echo "Recent trace IDs (if available):"
    tail -n 200 "$DUO_WORKFLOW_LOG" 2>/dev/null | grep -o 'trace[_-]id[":=][^",:}]*' | \
    sed 's/trace[_-]id[":=]//' | tail -5
    
    echo ""
    echo "To view traces:"
    echo "1. Go to https://smith.langchain.com/"
    echo "2. Select project: duo-workflow-local-debug"
    echo "3. Filter by correlation ID or time range"
    
    echo ""
    read -p "Press Enter to continue..."
}

service_health_check() {
    print_header
    echo -e "${GREEN}🏥 Service Health Check${NC}"
    echo ""
    
    # Check GitLab
    echo -n "GitLab Rails: "
    if curl -s http://localhost:3000/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Running${NC}"
    else
        echo -e "${RED}❌ Not responding${NC}"
    fi
    
    # Check AI Gateway
    echo -n "AI Gateway: "
    if curl -s http://localhost:5000/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Running${NC}"
    else
        echo -e "${RED}❌ Not responding${NC}"
    fi
    
    # Check Duo Workflow Service
    echo -n "Duo Workflow Service: "
    if grpcurl -plaintext localhost:50052 list >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Running${NC}"
    else
        echo -e "${RED}❌ Not responding${NC}"
    fi
    
    echo ""
    echo "Log file status:"
    [ -f "$GITLAB_LOG" ] && echo -e "GitLab Rails log: ${GREEN}✅ Available${NC}" || echo -e "GitLab Rails log: ${RED}❌ Missing${NC}"
    [ -f "$AI_GATEWAY_LOG" ] && echo -e "AI Gateway log: ${GREEN}✅ Available${NC}" || echo -e "AI Gateway log: ${RED}❌ Missing${NC}"
    [ -f "$DUO_WORKFLOW_LOG" ] && echo -e "Duo Workflow log: ${GREEN}✅ Available${NC}" || echo -e "Duo Workflow log: ${RED}❌ Missing${NC}"
    
    echo ""
    read -p "Press Enter to continue..."
}

main_menu() {
    while true; do
        print_header
        check_log_files
        print_menu
        
        read -p "Select option: " choice
        
        case $choice in
            1) real_time_monitoring ;;
            2) correlation_analysis ;;
            3) agent_orchestration_view ;;
            4) tool_execution_tracking ;;
            5) error_analysis ;;
            6) performance_metrics ;;
            7) langsmith_links ;;
            8) service_health_check ;;
            q|Q) echo "Goodbye!"; exit 0 ;;
            *) echo "Invalid option. Press Enter to continue..."; read ;;
        esac
    done
}

# Main execution
check_dependencies
main_menu
