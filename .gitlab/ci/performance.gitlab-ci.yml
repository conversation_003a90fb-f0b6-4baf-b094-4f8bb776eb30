dotenv-vars:
  stage: test
  needs:
    - job: build-docker-model-gateway
  script:
    - echo "JOB_WITH_ARTIFACTS=$CI_JOB_NAME" >> variables.env
    - echo "JOB_WITH_ARTIFACTS_ID=$CI_JOB_ID" >> variables.env
    - echo $JOB_WITH_ARTIFACTS
    - echo $JOB_WITH_ARTIFACTS_ID
    - echo $CI_PROJECT_NAMESPACE
    - cat variables.env
  artifacts:
    paths:
      - performance_tests
      - variables.env
    expire_in: 5d
    reports:
      dotenv: variables.env
  allow_failure: true
  rules:
    - if: $CI_PROJECT_NAMESPACE =~ /^gitlab-org\/security.*/
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_PROJECT_NAMESPACE =~ /^gitlab-org?($|\/)/'
      when: always
    - if: $CI_COMMIT_BRANCH == "main"
      when: always
    - when: never

tests:performance:
  stage: test
  needs:
    - job: dotenv-vars
      artifacts: true
  trigger:
    project: gitlab-org/quality/component-performance-testing
    branch: main
    strategy: depend
    forward:
      pipeline_variables: true
      yaml_variables: true
  allow_failure: true
  variables:
    JOB_WITH_ARTIFACTS: $JOB_WITH_ARTIFACTS
    JOB_WITH_ARTIFACTS_ID: $JOB_WITH_ARTIFACTS_ID
    PROJECT_ID: $CI_PROJECT_ID
    PARENT_PIPELINE_COMMIT_BRANCH: $CI_COMMIT_BRANCH
    PARENT_PIPELINE_SOURCE: $CI_PIPELINE_SOURCE
    COMPONENT_MR_CI_COMMIT_SHA: $CI_COMMIT_SHORT_SHA
    COMPONENT_SERVICE_NAME: "ai-gateway-influx"
    TEST_FILE_LOCATION: "performance_tests/k6_test"
    DOCKER_COMPOSE_FILE_LOCATION: "performance_tests/setup"
  rules:
    - if: $CI_PROJECT_NAMESPACE =~ /^gitlab-org\/security.*/
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_PROJECT_NAMESPACE =~ /^gitlab-org?($|\/)/'
      variables:
        PARENT_PIPELINE_CONTEXT: $CI_MERGE_REQUEST_IID
      when: always
    - if: $CI_COMMIT_BRANCH == "main"
      variables:
        PARENT_PIPELINE_CONTEXT: "main"
      when: always
    - when: never
