lint:
  extends: .poetry
  stage: test
  needs:
    - install
  script:
    - make lint-code
    - poetry lock --no-cache
    - git diff --exit-code
  after_script:
    - |
      # Hint for fixing issues
      MAGENTA=$(printf '\e[35m')
      BOLD=$(printf '\e[1m')
      RESET=$(printf '\e[0m')
      echo "${MAGENTA}Run ${BOLD}make format${RESET}${MAGENTA} to fix formatting issues.${RESET}"

lint:model_config_files:
  stage: test
  extends: .poetry
  needs:
    - install
  rules:
    - changes:
        - "ai_gateway/model_selection/*.{yml}"
  script:
    - make check-model-selection

lint:doc:
  stage: test
  image: ${DOCS_LINT_IMAGE}
  needs: []
  rules:
    - changes:
        - "**/*.{md}"
        - "{doc}/**/*"
  script:
    - make lint-doc

lint:commit:
  stage: test
  image: registry.hub.docker.com/library/node:alpine
  needs: []
  variables:
    GIT_DEPTH: 0
  before_script:
    - apk add --no-cache git
    - npm install
  script:
    - npx commitlint --from="$CI_MERGE_REQUEST_DIFF_BASE_SHA" --help-url
    - echo "Verifying Merge request title formatting"
    - echo "$CI_MERGE_REQUEST_TITLE" | npx commitlint
  rules:
    - if: "$CI_MERGE_REQUEST_DIFF_BASE_SHA"
