# Dependencies for Duo Workflow Service. Extended tests are run when these change patterns are matched.
# Note: Do not modify this file manually. Instead, run: make duo-workflow-service-dependencies
.duo-workflow-service-dependencies:
  changes:
    - duo_workflow_service/**/*
    - tests/duo_workflow_service/**/*
    - contract/**/*
    - pyproject.toml
    - ai_gateway/code_suggestions/language_server.py
    - ai_gateway/config.py
    - ai_gateway/container.py
    - ai_gateway/model_metadata.py
    - ai_gateway/models/*.py
    - ai_gateway/prompts/*.py
    - ai_gateway/prompts/config/base.py
    - ai_gateway/prompts/config/models.py
    - ai_gateway/prompts/registry.py
    - ai_gateway/searches/*.py
