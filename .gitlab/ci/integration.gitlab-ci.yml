########################
# Base job definitions #
########################

.integration-base-job:
  extends:
    - .verify-job-cached_variables
    - .rules:code-changes-verify
  image: ${INTEGRATION_IMAGE}
  stage: integration
  needs:
    - build-integration-image
  tags:
    - gitlab-org-docker
  variables:
    GIT_STRATEGY: none
  before_script:
    - cd /home/<USER>/gdk
    - !reference [.default-before_script, before_script]
    - gdk config set tool_version_manager.enabled true
  after_script:
    - !reference [.default-after_script, after_script]
  artifacts:
    paths:
      - gitlab_log/
      - gdk_log/
    expire_in: 2 days
    when: always
  timeout: 2h

###############################
# Integration job definitions #
###############################

integration:postgres-upgrade:
  extends:
    - .integration-base-job
    - .rules:postgres-upgrade
  script:
    - |
      CURRENT_POSTGRES_VERSION=$(grep '^postgres ' .tool-versions | awk '{print $2}')
      echo "Current version: $CURRENT_POSTGRES_VERSION"
      echo "Target version: $TARGET_POSTGRES_VERSION"
      if [ "$CURRENT_POSTGRES_VERSION" = "$TARGET_POSTGRES_VERSION" ]; then
        echo "Skipping job since the current is the same as the target version ($TARGET_POSTGRES_VERSION)."
        exit 0
      fi
    - source support/ci/functions.sh
    - init
    - cd_into_checkout_path "gitlab"
    - sed -i -E "s/(postgres) ([0-9]+\.[0-9]+) ([0-9]+\.[0-9]+)/\1 $TARGET_POSTGRES_VERSION \2/" .tool-versions
    - cd_into_checkout_path
    - sed -i -E "s/(postgres) ([0-9]+\.[0-9]+) ([0-9]+\.[0-9]+)/\1 $TARGET_POSTGRES_VERSION \2/" .tool-versions
    - run_timed_command "gdk config set pgvector.enabled true"
    - run_timed_command "GDK_SELF_UPDATE=0 gdk update"
    - run_timed_command "gdk start"
    - run_timed_command "test_url"

integration:performance:
  extends:
    - .integration-base-job
    - .rules:code-changes-mr-only
  tags: [saas-linux-large-amd64]
  variables:
    HYPERFINE_VERSION: v1.19.0
    HYPERFINE_PACKAGE_NAME: hyperfine_1.19.0_amd64.deb
  before_script:
    - !reference [.integration-base-job, before_script]
    - wget https://github.com/sharkdp/hyperfine/releases/download/${HYPERFINE_VERSION}/${HYPERFINE_PACKAGE_NAME}
    - sudo dpkg -i ${HYPERFINE_PACKAGE_NAME}
    - rm ${HYPERFINE_PACKAGE_NAME}
  script:
    - support/ci/performance
  allow_failure: true
