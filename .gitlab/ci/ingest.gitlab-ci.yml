include:
  # see https://gitlab.com/gitlab-com/gl-infra/common-ci-tasks/-/blob/main/oidc.md
  - project: 'gitlab-com/gl-infra/common-ci-tasks'
    ref: v2.88 # renovate:managed
    file: 'oidc.yml'

.ingest-base:
  image: $INGEST_IMAGE
  variables:
    SEARCH_APP_NAME: "gitlab-docs"
    GITLAB_DOCS_REPO: "https://gitlab.com/gitlab-org/gitlab.git"
    ********************: "master"
    GITLAB_DOCS_CLONE_DIR: "/tmp/gitlab-org/gitlab"
    GITLAB_DOCS_JSONL_EXPORT_PATH: "$CI_PROJECT_DIR/docs.jsonl"
    GITLAB_DOCS_WEB_ROOT_URL: "https://gitlab.com/help"
  needs: [build:ingest-image]

.ingest-dev:
  variables:
    GCP_PROJECT_NAME: $INGEST_GCP_PROJECT_NAME
    GCP_OIDC_WORKLOAD_ID_POOL_PROVIDER_NAME: $INGEST_WORKLOAD_IDENTITY_PROVIDER
    GCP_OIDC_SERVICE_ACCOUNT_EMAIL: $INGEST_SERVICE_ACCOUNT
  script:
    - cd /app && make ingest
  environment: ingest/dev

.ingest-prod:
  variables:
    GCP_PROJECT_NAME: $INGEST_GCP_PROJECT_NAME
    GCP_OIDC_WORKLOAD_ID_POOL_PROVIDER_NAME: $INGEST_WORKLOAD_IDENTITY_PROVIDER
    GCP_OIDC_SERVICE_ACCOUNT_EMAIL: $INGEST_SERVICE_ACCOUNT
  script:
    - cd /app && make ingest
  environment: ingest/prod

ingest:dev:
  stage: ingest
  extends: [.ingest-base, .oidc_base_gcp, .ingest-dev]
  rules:
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "ingest"

ingest:dev:trigger:
  stage: ingest
  extends: [.ingest-base, .oidc_base_gcp, .ingest-dev]
  when: manual
  rules:
    - if: $GITLAB_INGEST_TRIGGER == "dev"

ingest:prod:
  stage: ingest
  extends: [.ingest-base, .oidc_base_gcp, .ingest-prod]
  needs: [ingest:dev]
  rules:
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "ingest"

ingest:prod:trigger:
  stage: ingest
  extends: [.ingest-base, .oidc_base_gcp, .ingest-prod]
  rules:
    - if: $GITLAB_INGEST_TRIGGER == "prod"

ingest:dry-run:
  stage: ingest
  extends: [.ingest-base]
  variables:
    GCP_PROJECT_NAME: dummy
  script:
    - cd /app && make ingest INGEST_DRY_RUN=true
    - ./scripts/ingest/gitlab-docs/test.sh
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  artifacts:
    # can only pack files inside $CI_PROJECT_DIR
    # https://docs.gitlab.com/ee/ci/yaml/index.html#artifactspaths
    paths:
    - $GITLAB_DOCS_JSONL_EXPORT_PATH
