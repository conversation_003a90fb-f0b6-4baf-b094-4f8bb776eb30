install:
  extends: .poetry
  stage: build
  script:
    - poetry install

build-docker-model-gateway:
  stage: build
  extends:
    - .kaniko_base
  variables:
    KANIKO_BUILD_FILE: Dockerfile
    KANIKO_DESTINATION: "${TARGET_IMAGE}"

build:ingest-image:
  stage: build
  extends:
    - .kaniko_base
  variables:
    KAN<PERSON>KO_BUILD_FILE: scripts/ingest/Dockerfile
    KANIKO_DESTINATION: "${INGEST_IMAGE}"

build:self-hosted-ai-gateway-image:
  stage: build
  extends:
    - .kaniko_base
  variables:
    KANIKO_BUILD_FILE: docker/self_hosted/Dockerfile
    KANIKO_DESTINATION: "${TARGET_IMAGE}"
    KANIKO_EXTRA_ARGS: |
      --build-arg TAG=$CI_COMMIT_TAG
      --build-arg SRC_IMAGE="${TARGET_IMAGE}"
  rules:
    - if: $CI_COMMIT_TAG =~ /^self-hosted-/
  needs:
    - build-docker-model-gateway

build:self-hosted-ai-gateway-latest-image:
  stage: build
  extends:
    - .kaniko_base
  variables:
    <PERSON><PERSON><PERSON><PERSON>_BUILD_FILE: docker/self_hosted/Dockerfile
    KANIKO_DESTINATION: "${SELF_HOSTED_TARGET_IMAGE}"
    KANIKO_EXTRA_ARGS: |
      --build-arg TAG=master
      --build-arg SRC_IMAGE="${TARGET_IMAGE}"
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  needs:
    - build-docker-model-gateway
