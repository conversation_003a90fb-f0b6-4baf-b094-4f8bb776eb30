<!--
Please read this!

Before opening a new issue, make sure to search for keywords in the issues:

- https://gitlab.com/gitlab-org/modelops/applied-ml/code-suggestions/ai-assist/-/issues

and verify the issue you're about to submit isn't a duplicate.
-->

## Problem to solve

<!--
What problem do we solve? Try to define the who/what/why of the opportunity as a
user story.

For example, "As a (who), I want (what), so I can (why/value)."
-->

## Proposal

<!-- How are we going to solve the problem? -->

## Further details

<!--
Include examples, use cases, benefits, goals, or any other details that help us
understand the problem better.
-->

## Links / references

/label ~"group::ai framework"

<!-- Select a type -->
<!-- /label ~"type::bug" -->
<!-- /label ~"type::feature" -->
<!-- /label ~"type::maintenance" -->
