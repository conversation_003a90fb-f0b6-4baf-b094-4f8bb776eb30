# GitLab Duo Agent Platform (DAP) Debugging Guide

This guide will help you debug and trace the complete flow of GitLab DAP through the VS Code extension.

## 🚀 Quick Start

1. **Start comprehensive monitoring:**
   ```bash
   cd ../gitlab-vscode-extension
   ./test-gitlab-extension.sh start
   ```

2. **Open VS Code extension project:**
   ```bash
   cd ../gitlab-vscode-extension
   code .
   ```

3. **Launch extension in debug mode:**
   - Press `F5` or go to Run & Debug
   - Select "Debug Extension with Duo Platform"
   - This opens a new VS Code window with your extension loaded

## 📊 Monitoring & Logging

### Real-time Log Monitoring

The monitoring script tracks these log files:

- **AI Gateway Debug**: `logs/ai-gateway-monitor.log`
- **AI Gateway Service**: `logs/ai-gateway-service.log` 
- **Duo Workflow Service**: `logs/duo-workflow-monitor.log`
- **GitLab Rails AI Features**: `logs/gitlab-ai-logs.log`

### Manual Log Locations

- **GitLab Rails**: `gdk/gitlab/log/development.log`
- **AI Gateway**: `gdk/log/gitlab-ai-gateway/gateway_debug.log`
- **Duo Workflow**: `gdk/log/duo-workflow-service/current`
- **VS Code Extension**: VS Code Output Panel → "GitLab Workflow"

## 🔍 End-to-End Flow Tracing

When you send a query through VS Code DAP, here's the complete flow:

### 1. VS Code Extension (Client)
- **Location**: Extension Development Host window
- **Logs**: View → Output → "GitLab Workflow"
- **What to look for**:
  - DAP connection establishment
  - Query serialization
  - gRPC calls to GitLab

### 2. GitLab Rails (API Layer)
- **Location**: `gdk/gitlab/log/development.log`
- **What to look for**:
  - API requests to `/api/v4/ai/duo_agent_platform/*`
  - Authentication and authorization
  - Request forwarding to AI Gateway

### 3. AI Gateway (Orchestration)
- **Location**: `gdk/log/gitlab-ai-gateway/gateway_debug.log`
- **What to look for**:
  - Incoming requests from GitLab
  - Tool selection and execution
  - Requests to external AI models
  - Response processing

### 4. Duo Workflow Service (Agent Execution)
- **Location**: `gdk/log/duo-workflow-service/current`
- **What to look for**:
  - Agent graph execution
  - Tool invocations
  - State transitions
  - Final response generation

## 🛠️ Testing DAP Features

### Basic Chat Query
1. In Extension Development Host, open Command Palette (`Cmd+Shift+P`)
2. Run "GitLab: Open Duo Chat"
3. Send a simple query like "What is this project about?"
4. Watch logs for the complete flow

### Code Suggestions
1. Open a code file in Extension Development Host
2. Start typing code
3. Watch for suggestion requests in logs

### Tool Usage Testing
Send queries that would trigger specific tools:
- "What files are in this repository?" → File system tools
- "What's the latest commit?" → Git tools
- "Explain this function" → Code analysis tools

## 🔧 Debugging Tips

### Enable Maximum Verbosity
All logging is already configured for maximum verbosity:
- VS Code extension: `GITLAB_DEBUG=true`, `VSCODE_GITLAB_VERBOSE_LOGGING=true`
- AI Gateway: Request/response logging enabled
- Duo Workflow: Debug mode enabled
- GitLab Rails: Verbose AI logs enabled

### Common Issues & Solutions

1. **Connection Issues**:
   - Check `gdk status` - ensure all services are running
   - Verify VS Code settings point to `http://127.0.0.1:3000`

2. **Authentication Issues**:
   - Ensure you're logged in to local GitLab in VS Code
   - Check token permissions in GitLab admin

3. **No DAP Features**:
   - Verify Ultimate license is active
   - Check Duo Pro seat assignment
   - Ensure AI Gateway is connected

### Log Analysis Commands

```bash
# Watch all AI-related logs in real-time
tail -f logs/*.log

# Search for specific patterns
grep -i "error\|exception" logs/*.log
grep -i "tool\|agent" logs/*.log
grep -i "grpc\|api" logs/*.log

# Monitor specific service
tail -f ../gdk/log/gitlab-ai-gateway/gateway_debug.log
```

## 🎯 What to Look For

When testing DAP, trace these key events:

1. **Extension → GitLab**: gRPC connection, authentication
2. **GitLab → AI Gateway**: API forwarding, request validation  
3. **AI Gateway → Duo Workflow**: Agent invocation, tool selection
4. **Tool Execution**: File system access, Git operations, etc.
5. **Response Flow**: Result processing, streaming back to client

## 📝 Stopping Monitoring

```bash
./test-gitlab-extension.sh stop
```

This stops all background log monitoring processes.
