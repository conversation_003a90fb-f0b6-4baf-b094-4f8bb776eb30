DUO_WORKFLOW_AUTH__OIDC_GITLAB_URL: "https://staging.gitlab.com/"
DUO_WORKFLOW_AUTH__OIDC_CUSTOMER_PORTAL_URL: "https://customers.staging.gitlab.com"
DUO_WORKFLOW_GOOGLE_CLOUD_PROFILER__ENABLED: true
LANGCHAIN_TRACING_V2: true
LANGSMITH_PROJECT: "duo-workflow-staging"
SENTRY_ERROR_TRACKING_ENABLED: true
DUO_WORKFLOW_CLOUD_CONNECTOR_SERVICE_NAME: "gitlab-********************"
DUO_WORKFLOW_SERVICE_ENVIRONMENT: "staging"
AIGW_INTERNAL_EVENT__ENABLED: true
AIGW_INTERNAL_EVENT__ENDPOINT: "https://snowplowprd.trx.gitlab.net"
AIGW_INTERNAL_EVENT__APP_ID: "gitlab_duo_workflow"
AIGW_INTERNAL_EVENT__BATCH_SIZE: 1
AIGW_INTERNAL_EVENT__THREAD_COUNT: 1
# https://github.com/grpc/grpc/blob/master/doc/trace_flags.md
GRPC_TRACE: "http_keepalive"
# https://github.com/grpc/grpc/blob/master/doc/environment_variables.md
GRPC_VERBOSITY: "ERROR"
PROMETHEUS_METRICS__ADDR: "0.0.0.0"
PROMETHEUS_METRICS__PORT: "8082"
WORKFLOW_INTERRUPT: true
FEATURE_GOAL_DISAMBIGUATION: true
DISABLE_AI_GATEWAY: "true"
ENABLE_DUO_WORKFLOW_SERVICE: "true"
DUO_WORKFLOW_DIRECT_CONNECTION_BASE_URL: "https://staging.gitlab.com"
AIGW_GOOGLE_CLOUD_PLATFORM__PROJECT: "gitlab-ai-framework-stage"
AIGW_VERTEX_SEARCH__FALLBACK_DATASTORE_VERSION: "17.0"
