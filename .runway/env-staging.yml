AIGW_FASTAPI__API_PORT: "8080"
AIGW_ENVIRONMENT: "staging"
AIGW_GITLAB_URL: "https://staging.gitlab.com"
AIGW_GITLAB_API_URL: "https://staging.gitlab.com/api/v4/"
AIGW_CUSTOMER_PORTAL_URL: "https://customers.staging.gitlab.com"
AIGW_GLGO_BASE_URL: "https://glgo.staging.runway.gitlab.net"
AIGW_GOOGLE_CLOUD_PLATFORM__PROJECT: "gitlab-ai-framework-stage"
AIGW_VERTEX_SEARCH__FALLBACK_DATASTORE_VERSION: "17.0"
AIGW_SNOWPLOW__ENABLED: "false"
AIGW_INTERNAL_EVENT__ENABLED: "false"
AIGW_BILLING_EVENT__ENABLED: "false"
HOME: "/root"
AIGW_FASTAPI__METRICS_HOST: "0.0.0.0"
AIGW_FASTAPI__METRICS_PORT: "8082"
AIGW_GOOGLE_CLOUD_PROFILER__ENABLED: "true"
AIGW_GOOGLE_CLOUD_PROFILER__VERBOSE: "2"
AIGW_GOOGLE_CLOUD_PROFILER__PERIODS_MS: "10"
AIGW_INSTRUMENTATOR__THREAD_MONITORING_ENABLED: "True"
AIGW_INSTRUMENTATOR__THREAD_MONITORING_INTERVAL: 60
AIGW_CLOUD_CONNECTOR_SERVICE_NAME: "gitlab-ai-gateway"
AIGW_MODEL_ENDPOINTS__FIREWORKS_REGIONAL_ENDPOINTS: '{ "us": {"qwen2p5-coder-7b": { "endpoint": "https://gitlab-ac589058.us-virginia-1.direct.fireworks.ai/v1", "identifier": "accounts/fireworks/models/qwen2p5-coder-7b#accounts/gitlab/deployments/ac589058"}, "codestral-2501": {"endpoint": "https://gitlab-fd046d6c.us-arizona-1.direct.fireworks.ai/v1","identifier": "accounts/gitlab/models/codestral-2501"}}}'
AIGW_AMAZON_Q__REGION: "us-east-1"
AIGW_AMAZON_Q__ENDPOINT_URL: "https://us-east-1.prod.integration.qdev.ai.aws.dev"
AIGW_FEATURE_FLAGS__FIREWORKS_SCORE_THRESHOLD: '{"qwen2p5-coder-7b": -1.5}'
