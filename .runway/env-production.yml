AIGW_FASTAPI__API_PORT: "8080"
AIGW_ENVIRONMENT: "production"
AIGW_GITLAB_URL: "https://gitlab.com"
AIGW_GITLAB_API_URL: "https://gitlab.com/api/v4/"
AIGW_CUSTOMER_PORTAL_URL: "https://customers.gitlab.com"
AIGW_GLGO_BASE_URL: "https://auth.token.gitlab.com"
AIGW_GOOGLE_CLOUD_PLATFORM__PROJECT: "gitlab-ai-framework-prod"
AIGW_VERTEX_SEARCH__FALLBACK_DATASTORE_VERSION: "17.0"
AIGW_SNOWPLOW__ENABLED: "true"
AIGW_SNOWPLOW__ENDPOINT: "https://snowplowprd.trx.gitlab.net"
AIGW_SNOWPLOW__BATCH_SIZE: 1
AIGW_SNOWPLOW__THREAD_COUNT: 1
AIGW_INTERNAL_EVENT__ENABLED: "true"
AIGW_INTERNAL_EVENT__ENDPOINT: "https://snowplowprd.trx.gitlab.net"
AIGW_INTERNAL_EVENT__BATCH_SIZE: 1
AIGW_INTERNAL_EVENT__THREAD_COUNT: 1
AIGW_BILLING_EVENT__ENABLED: "false"
AIGW_BILLING_EVENT__ENDPOINT: "https://snowplowprd.trx.gitlab.net"
AIGW_BILLING_EVENT__NAMESPACE: "gl"
AIGW_BILLING_EVENT__BATCH_SIZE: 10
AIGW_BILLING_EVENT__THREAD_COUNT: 1
HOME: "/root"
AIGW_FASTAPI__METRICS_HOST: "0.0.0.0"
AIGW_FASTAPI__METRICS_PORT: "8082"
AIGW_GOOGLE_CLOUD_PROFILER__ENABLED: "true"
AIGW_GOOGLE_CLOUD_PROFILER__VERBOSE: "2"
AIGW_GOOGLE_CLOUD_PROFILER__PERIODS_MS: "10"
AIGW_INSTRUMENTATOR__THREAD_MONITORING_ENABLED: "True"
AIGW_INSTRUMENTATOR__THREAD_MONITORING_INTERVAL: 60
AIGW_FEATURE_FLAGS__DISALLOWED_FLAGS: '{"self-managed": ["expanded_ai_logging"]}'
AIGW_CLOUD_CONNECTOR_SERVICE_NAME: "gitlab-ai-gateway"
AIGW_MODEL_ENDPOINTS__FIREWORKS_REGIONAL_ENDPOINTS: '{ "us": { "qwen2p5-coder-7b": { "endpoint": "https://gitlab-84f6d9f4.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/fireworks/models/qwen2p5-coder-7b#accounts/gitlab/deployments/84f6d9f4" }, "codestral-2501": { "endpoint": "https://gitlab-a8a5a0a6.us-virginia-1.direct.fireworks.ai/v1", "identifier": "accounts/gitlab/models/codestral-2501" } }, "europe": { "qwen2p5-coder-7b": { "endpoint": "https://gitlab-84f6d9f4.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/fireworks/models/qwen2p5-coder-7b#accounts/gitlab/deployments/84f6d9f4" }, "codestral-2501": { "endpoint": "https://gitlab-6fd60574.eu-frankfurt-1.direct.fireworks.ai/v1", "identifier": "accounts/gitlab/models/codestral-2501" } }, "asia": { "qwen2p5-coder-7b": { "endpoint": "https://gitlab-84f6d9f4.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/fireworks/models/qwen2p5-coder-7b#accounts/gitlab/deployments/84f6d9f4" }, "codestral-2501": { "endpoint": "https://gitlab-3065d573.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/gitlab/models/codestral-2501" } }}'
AIGW_AMAZON_Q__REGION: "us-east-1"
AIGW_AMAZON_Q__ENDPOINT_URL: "https://us-east-1.prod.integration.qdev.ai.aws.dev"
AIGW_FEATURE_FLAGS__FIREWORKS_SCORE_THRESHOLD: '{"qwen2p5-coder-7b": -1.5}'
