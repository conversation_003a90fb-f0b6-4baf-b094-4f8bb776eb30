AIGW_FASTAPI__API_PORT: "8080"
AIGW_ENVIRONMENT: "staging-ref"
AIGW_GITLAB_URL: "https://staging.gitlab.com"
AIGW_GITLAB_API_URL: "https://staging.gitlab.com/api/v4/"
AIGW_CUSTOMER_PORTAL_URL: "https://customers.staging.gitlab.com"
AIGW_GLGO_BASE_URL: "https://glgo.staging.runway.gitlab.net"
AIGW_SNOWPLOW__ENABLED: "false"
AIGW_INTERNAL_EVENT__ENABLED: "false"
HOME: "/root"
AIGW_FASTAPI__METRICS_HOST: "0.0.0.0"
AIGW_FASTAPI__METRICS_PORT: "8082"
AIGW_GOOGLE_CLOUD_PROFILER__ENABLED: "false"
AIGW_INSTRUMENTATOR__THREAD_MONITORING_ENABLED: "True"
AIGW_INSTRUMENTATOR__THREAD_MONITORING_INTERVAL: 60
AIGW_CLOUD_CONNECTOR_SERVICE_NAME: "gitlab-ai-gateway"
AIGW_MODEL_ENDPOINTS__FIREWORKS_REGIONAL_ENDPOINTS: '{ "us": { "qwen2p5-coder-7b": { "endpoint": "https://gitlab-84f6d9f4.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/fireworks/models/qwen2p5-coder-7b#accounts/gitlab/deployments/84f6d9f4" }, "codestral-2501": { "endpoint": "https://gitlab-a8a5a0a6.us-virginia-1.direct.fireworks.ai/v1", "identifier": "accounts/gitlab/models/codestral-2501" } }, "europe": { "qwen2p5-coder-7b": { "endpoint": "https://gitlab-84f6d9f4.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/fireworks/models/qwen2p5-coder-7b#accounts/gitlab/deployments/84f6d9f4" }, "codestral-2501": { "endpoint": "https://gitlab-6fd60574.eu-frankfurt-1.direct.fireworks.ai/v1", "identifier": "accounts/gitlab/models/codestral-2501" } }, "asia": { "qwen2p5-coder-7b": { "endpoint": "https://gitlab-84f6d9f4.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/fireworks/models/qwen2p5-coder-7b#accounts/gitlab/deployments/84f6d9f4" }, "codestral-2501": { "endpoint": "https://gitlab-3065d573.tokyo.direct.fireworks.ai/v1", "identifier": "accounts/gitlab/models/codestral-2501" } }}'
AIGW_FEATURE_FLAGS__FIREWORKS_SCORE_THRESHOLD: '{"qwen2p5-coder-7b": -1.5}'
AIGW_AMAZON_Q__REGION: "us-west-2"
AIGW_AMAZON_Q__ENDPOINT_URL: "https://us-west-2.gamma.integration.qdev.ai.aws.dev"
