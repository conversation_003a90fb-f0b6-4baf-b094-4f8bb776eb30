#!/bin/bash

# GitLab DAP Correlation ID Setup Script
# Ensures correlation IDs flow properly across all DAP services

set -e

echo "🔗 GitLab DAP Correlation ID Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if we're in the right directory
check_directory() {
    if [ ! -d "gitlab-development-kit" ]; then
        print_error "This script must be run from the gdk root directory"
        exit 1
    fi
}

# Configure GitLab Rails for correlation ID propagation
configure_gitlab_rails() {
    print_info "Configuring GitLab Rails for correlation ID propagation..."
    
    GITLAB_CONFIG="gitlab/config/application.rb"
    
    if [ -f "$GITLAB_CONFIG" ]; then
        # Check if correlation ID middleware is already configured
        if ! grep -q "correlation_id" "$GITLAB_CONFIG" 2>/dev/null; then
            print_info "Adding correlation ID configuration to GitLab Rails..."
            
            # Add correlation ID configuration
            cat >> "$GITLAB_CONFIG" << 'EOF'

# DAP Correlation ID Configuration
config.middleware.use ActionDispatch::RequestId
config.log_tags = [:request_id]

# Ensure correlation IDs are propagated to AI Gateway
Rails.application.configure do
  config.after_initialize do
    # Add correlation ID to outgoing HTTP requests
    if defined?(Faraday)
      Faraday.default_connection.use :request_id
    end
  end
end
EOF
            print_status "Correlation ID configuration added to GitLab Rails"
        else
            print_status "Correlation ID already configured in GitLab Rails"
        fi
    else
        print_warning "GitLab Rails configuration file not found"
    fi
}

# Configure AI Gateway for correlation ID handling
configure_ai_gateway() {
    print_info "Configuring AI Gateway for correlation ID handling..."
    
    AI_GATEWAY_ENV="gitlab-development-kit/gitlab-ai-gateway/.env"
    
    # Add correlation ID configuration
    if ! grep -q "CORRELATION_ID" "$AI_GATEWAY_ENV" 2>/dev/null; then
        cat >> "$AI_GATEWAY_ENV" << 'EOF'

# Correlation ID Configuration
AIGW_CORRELATION_ID__ENABLED=true
AIGW_CORRELATION_ID__HEADER_NAME=X-Request-ID
AIGW_CORRELATION_ID__PROPAGATE_TO_LANGSMITH=true
EOF
        print_status "Correlation ID configuration added to AI Gateway"
    else
        print_status "Correlation ID already configured in AI Gateway"
    fi
}

# Configure Duo Workflow Service for correlation ID handling
configure_duo_workflow() {
    print_info "Configuring Duo Workflow Service for correlation ID handling..."
    
    DUO_WORKFLOW_ENV="gitlab-development-kit/gitlab-ai-gateway/********************/.env"
    
    # Add correlation ID configuration
    if ! grep -q "CORRELATION_ID" "$DUO_WORKFLOW_ENV" 2>/dev/null; then
        cat >> "$DUO_WORKFLOW_ENV" << 'EOF'

# Correlation ID Configuration
DUO_WORKFLOW_CORRELATION_ID__ENABLED=true
DUO_WORKFLOW_CORRELATION_ID__HEADER_NAME=X-Request-ID
DUO_WORKFLOW_CORRELATION_ID__PROPAGATE_TO_LANGSMITH=true
EOF
        print_status "Correlation ID configuration added to Duo Workflow Service"
    else
        print_status "Correlation ID already configured in Duo Workflow Service"
    fi
}

# Create correlation ID test script
create_correlation_test() {
    print_info "Creating correlation ID test script..."
    
    cat > test-correlation-ids.sh << 'EOF'
#!/bin/bash

# Test Correlation ID Propagation
# This script tests that correlation IDs flow properly through all DAP services

set -e

echo "🔗 Testing DAP Correlation ID Propagation"
echo "========================================"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Generate a test correlation ID
CORRELATION_ID="test-$(date +%Y%m%d%H%M%S)-$$"
echo -e "${YELLOW}Test Correlation ID: $CORRELATION_ID${NC}"
echo ""

# Test GitLab API with correlation ID
echo "1. Testing GitLab API..."
GITLAB_RESPONSE=$(curl -s -H "X-Request-ID: $CORRELATION_ID" \
    -H "Authorization: Bearer $GDK_API_TOKEN" \
    "http://localhost:3000/api/v4/projects" | head -c 100)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ GitLab API responded${NC}"
else
    echo -e "${RED}✗ GitLab API failed${NC}"
fi

# Check if correlation ID appears in GitLab logs
echo ""
echo "2. Checking GitLab logs for correlation ID..."
if tail -n 50 gitlab/log/development.log | grep -q "$CORRELATION_ID"; then
    echo -e "${GREEN}✓ Correlation ID found in GitLab logs${NC}"
else
    echo -e "${RED}✗ Correlation ID not found in GitLab logs${NC}"
fi

# Test AI Gateway health endpoint
echo ""
echo "3. Testing AI Gateway..."
AI_GATEWAY_RESPONSE=$(curl -s -H "X-Request-ID: $CORRELATION_ID" \
    "http://localhost:5000/health" 2>/dev/null || echo "failed")

if [ "$AI_GATEWAY_RESPONSE" != "failed" ]; then
    echo -e "${GREEN}✓ AI Gateway responded${NC}"
else
    echo -e "${RED}✗ AI Gateway failed${NC}"
fi

# Check AI Gateway logs
echo ""
echo "4. Checking AI Gateway logs for correlation ID..."
if tail -n 50 log/gitlab-ai-gateway/gateway_debug.log 2>/dev/null | grep -q "$CORRELATION_ID"; then
    echo -e "${GREEN}✓ Correlation ID found in AI Gateway logs${NC}"
else
    echo -e "${RED}✗ Correlation ID not found in AI Gateway logs${NC}"
fi

# Test Duo Workflow Service
echo ""
echo "5. Testing Duo Workflow Service..."
DUO_WORKFLOW_RESPONSE=$(grpcurl -plaintext -H "x-request-id: $CORRELATION_ID" \
    localhost:50052 list 2>/dev/null || echo "failed")

if [ "$DUO_WORKFLOW_RESPONSE" != "failed" ]; then
    echo -e "${GREEN}✓ Duo Workflow Service responded${NC}"
else
    echo -e "${RED}✗ Duo Workflow Service failed${NC}"
fi

# Check Duo Workflow logs
echo ""
echo "6. Checking Duo Workflow logs for correlation ID..."
if tail -n 50 log/duo-workflow-service/current 2>/dev/null | grep -q "$CORRELATION_ID"; then
    echo -e "${GREEN}✓ Correlation ID found in Duo Workflow logs${NC}"
else
    echo -e "${RED}✗ Correlation ID not found in Duo Workflow logs${NC}"
fi

echo ""
echo "Test completed. Check the logs above for correlation ID propagation."
echo "To trace this request end-to-end, use:"
echo "  ./analyze-dap-traces.sh trace $CORRELATION_ID"
EOF

    chmod +x test-correlation-ids.sh
    print_status "Correlation ID test script created: test-correlation-ids.sh"
}

# Create correlation ID monitoring script
create_correlation_monitor() {
    print_info "Creating correlation ID monitoring script..."
    
    cat > monitor-correlation-ids.sh << 'EOF'
#!/bin/bash

# Monitor Correlation ID Flow
# Watches all DAP logs for correlation ID propagation

set -e

echo "🔗 Monitoring DAP Correlation ID Flow"
echo "===================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

# Log files to monitor
GITLAB_LOG="gitlab/log/development.log"
AI_GATEWAY_LOG="log/gitlab-ai-gateway/gateway_debug.log"
DUO_WORKFLOW_LOG="log/duo-workflow-service/current"

echo "Monitoring correlation ID flow across:"
echo "  • GitLab Rails: $GITLAB_LOG"
echo "  • AI Gateway: $AI_GATEWAY_LOG"
echo "  • Duo Workflow: $DUO_WORKFLOW_LOG"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop monitoring${NC}"
echo ""

# Monitor all logs for correlation IDs
tail -f "$GITLAB_LOG" "$AI_GATEWAY_LOG" "$DUO_WORKFLOW_LOG" 2>/dev/null | \
while IFS= read -r line; do
    case "$line" in
        *"==> $GITLAB_LOG <=="*)
            echo -e "${GREEN}[GitLab Rails]${NC}"
            ;;
        *"==> $AI_GATEWAY_LOG <=="*)
            echo -e "${BLUE}[AI Gateway]${NC}"
            ;;
        *"==> $DUO_WORKFLOW_LOG <=="*)
            echo -e "${YELLOW}[Duo Workflow]${NC}"
            ;;
        *correlation_id*|*request_id*|*X-Request-ID*)
            # Extract correlation ID from the line
            CORRELATION_ID=$(echo "$line" | grep -o '[a-zA-Z0-9-]*-[0-9]\{8\}T[0-9]\{6\}-[0-9]\{4\}' | head -1)
            if [ -n "$CORRELATION_ID" ]; then
                echo -e "${CYAN}🔗 CORRELATION: $CORRELATION_ID${NC} - $line"
            else
                echo -e "${CYAN}🔗${NC} $line"
            fi
            ;;
        *)
            echo "$line"
            ;;
    esac
done
EOF

    chmod +x monitor-correlation-ids.sh
    print_status "Correlation ID monitoring script created: monitor-correlation-ids.sh"
}

# Main execution
main() {
    echo ""
    print_info "Setting up DAP correlation ID propagation..."
    echo ""
    
    check_directory
    configure_gitlab_rails
    configure_ai_gateway
    configure_duo_workflow
    create_correlation_test
    create_correlation_monitor
    
    echo ""
    print_status "DAP correlation ID setup complete!"
    echo ""
    print_info "Next steps:"
    echo "1. Restart services: gdk restart"
    echo "2. Test correlation IDs: ./test-correlation-ids.sh"
    echo "3. Monitor correlation flow: ./monitor-correlation-ids.sh"
    echo ""
    print_info "For VS Code extension correlation IDs:"
    echo "1. The extension now generates correlation IDs automatically"
    echo "2. Check 'GitLab Workflow' output panel for trace logs"
    echo "3. Use correlation IDs to trace requests end-to-end"
    echo ""
}

main "$@"
