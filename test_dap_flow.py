#!/usr/bin/env python3
"""
Test script to demonstrate GitLab DAP flow tracing
This script helps you understand what to look for in the logs
"""

import subprocess
import sys

def test_gitlab_api():
    """Test basic GitLab API connectivity"""
    print("🔍 Testing GitLab API connectivity...")

    try:
        result = subprocess.run(
            ["curl", "-s", "-I", "http://127.0.0.1:3000/api/v4/version"],
            capture_output=True, text=True, timeout=5
        )
        if "200 OK" in result.stdout:
            print("✅ GitLab API is accessible")
            return True
        else:
            print("❌ GitLab API is not accessible")
            return False
    except Exception as e:
        print(f"❌ GitLab API connection failed: {e}")
        return False

def test_ai_gateway():
    """Test AI Gateway connectivity"""
    print("🔍 Testing AI Gateway connectivity...")

    try:
        result = subprocess.run(
            ["curl", "-s", "-I", "http://127.0.0.1:5052/monitoring/healthz"],
            capture_output=True, text=True, timeout=5
        )
        if "200 OK" in result.stdout:
            print("✅ AI Gateway is accessible")
            return True
        else:
            print("❌ AI Gateway is not accessible")
            return False
    except Exception as e:
        print(f"❌ AI Gateway connection failed: {e}")
        return False

def show_log_locations():
    """Show where to find logs for debugging"""
    print("\n📊 Log Locations for DAP Flow Tracing:")
    print("=" * 50)
    
    log_locations = [
        ("VS Code Extension", "VS Code Output Panel → 'GitLab Workflow'"),
        ("GitLab Rails", "../gdk/gitlab/log/development.log"),
        ("AI Gateway Debug", "../gdk/log/gitlab-ai-gateway/gateway_debug.log"),
        ("AI Gateway Service", "../gdk/log/gitlab-ai-gateway/current"),
        ("Duo Workflow", "../gdk/log/duo-workflow-service/current"),
        ("Monitoring Logs", "../gitlab-vscode-extension/logs/")
    ]
    
    for service, location in log_locations:
        print(f"📁 {service:20} → {location}")

def show_dap_flow():
    """Show the expected DAP flow"""
    print("\n🔄 Expected DAP Flow:")
    print("=" * 30)
    
    flow_steps = [
        "1. VS Code Extension → gRPC call to GitLab",
        "2. GitLab Rails → API validation & forwarding",
        "3. AI Gateway → Request routing & tool selection",
        "4. Duo Workflow Service → Agent execution",
        "5. Tool Execution → File system, Git, etc.",
        "6. Response Flow → Back through the chain"
    ]
    
    for step in flow_steps:
        print(f"   {step}")

def show_test_queries():
    """Show example queries to test DAP"""
    print("\n💬 Test Queries for DAP:")
    print("=" * 25)
    
    queries = [
        ("Basic Chat", "What is this project about?"),
        ("File System", "What files are in this repository?"),
        ("Git Operations", "What's the latest commit?"),
        ("Code Analysis", "Explain this function"),
        ("Tool Usage", "Create a new file called test.md")
    ]
    
    for category, query in queries:
        print(f"🔹 {category:15} → '{query}'")

def main():
    print("🚀 GitLab DAP Flow Testing & Debugging")
    print("=" * 40)

    # Test connectivity
    gitlab_ok = test_gitlab_api()
    ai_gateway_ok = test_ai_gateway()

    if gitlab_ok and ai_gateway_ok:
        print("\n✅ All services are accessible!")
    else:
        print("\n⚠️  Some services may not be accessible, but you can still proceed with debugging.")
    
    # Show debugging information
    show_log_locations()
    show_dap_flow()
    show_test_queries()
    
    print("\n🎯 Next Steps:")
    print("1. Open VS Code extension project: cd ../gitlab-vscode-extension && code .")
    print("2. Press F5 and select 'Debug Extension with Duo Platform'")
    print("3. In the Extension Development Host, open GitLab Duo Chat")
    print("4. Send one of the test queries above")
    print("5. Watch the logs in real-time to trace the complete flow")
    
    print("\n📝 Log Monitoring:")
    print("   tail -f ../gitlab-vscode-extension/logs/*.log")

if __name__ == "__main__":
    main()
