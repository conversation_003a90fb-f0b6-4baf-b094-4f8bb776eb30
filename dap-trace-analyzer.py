#!/usr/bin/env python3
"""
GitLab DAP Trace Analyzer
Comprehensive analysis and correlation of DAP traces across all services
"""

import json
import re
import sys
from collections import defaultdict, OrderedDict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse

class DAPTraceAnalyzer:
    def __init__(self, trace_dir: str = "dap-traces"):
        self.trace_dir = Path(trace_dir)
        self.correlation_map = defaultdict(list)
        self.agent_flows = defaultdict(list)
        self.tool_executions = defaultdict(list)
        self.request_timeline = []
        
    def parse_log_entry(self, line: str, source: str) -> Optional[Dict]:
        """Parse a log entry and extract structured information"""
        try:
            # Try JSON parsing first
            if line.strip().startswith('{'):
                data = json.loads(line.strip())
                return {
                    'timestamp': data.get('timestamp', data.get('@timestamp', '')),
                    'level': data.get('level', data.get('severity', 'INFO')),
                    'message': data.get('message', data.get('event', '')),
                    'correlation_id': data.get('correlation_id', data.get('x-request-id', '')),
                    'workflow_id': data.get('workflow_id', ''),
                    'logger': data.get('logger', data.get('logger_name', source)),
                    'source': source,
                    'raw': line.strip()
                }
            else:
                # Parse structured text logs
                timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2})', line)
                correlation_match = re.search(r'\[([a-f0-9-]{36}|[a-zA-Z0-9-]{8,})\]', line)
                level_match = re.search(r'\b(DEBUG|INFO|WARN|ERROR|FATAL)\b', line)
                
                return {
                    'timestamp': timestamp_match.group(1) if timestamp_match else '',
                    'level': level_match.group(1) if level_match else 'INFO',
                    'message': line.strip(),
                    'correlation_id': correlation_match.group(1) if correlation_match else '',
                    'workflow_id': '',
                    'logger': source,
                    'source': source,
                    'raw': line.strip()
                }
        except Exception as e:
            return {
                'timestamp': '',
                'level': 'ERROR',
                'message': f"Parse error: {str(e)}",
                'correlation_id': '',
                'workflow_id': '',
                'logger': source,
                'source': source,
                'raw': line.strip()
            }
    
    def load_traces(self):
        """Load and parse all trace files"""
        print("🔍 Loading DAP traces...")
        
        # Load filtered logs
        filtered_dir = self.trace_dir / "filtered"
        if filtered_dir.exists():
            for log_file in filtered_dir.glob("*.log"):
                source = log_file.stem
                print(f"  📄 Loading {source} traces...")
                
                try:
                    with open(log_file, 'r') as f:
                        for line in f:
                            if line.strip():
                                entry = self.parse_log_entry(line, source)
                                if entry:
                                    self.process_entry(entry)
                except Exception as e:
                    print(f"  ⚠️  Error loading {log_file}: {e}")
        
        print(f"✅ Loaded traces for {len(self.correlation_map)} correlation IDs")
    
    def process_entry(self, entry: Dict):
        """Process a log entry and categorize it"""
        correlation_id = entry.get('correlation_id', 'unknown')
        if correlation_id and correlation_id != 'unknown':
            self.correlation_map[correlation_id].append(entry)
        
        # Categorize agent activities
        message = entry.get('message', '').lower()
        if any(agent in message for agent in ['chatagent', 'planneragent', 'toolsexecutor', 'handoveragent']):
            workflow_id = entry.get('workflow_id', correlation_id)
            self.agent_flows[workflow_id].append(entry)
        
        # Categorize tool executions
        if any(tool in message for tool in ['tool_call', 'tool_response', 'tool_execution', 'run_command']):
            workflow_id = entry.get('workflow_id', correlation_id)
            self.tool_executions[workflow_id].append(entry)
        
        # Add to timeline
        if entry.get('timestamp'):
            self.request_timeline.append(entry)
    
    def analyze_request_flow(self, correlation_id: str) -> Dict:
        """Analyze the complete flow for a specific correlation ID"""
        entries = self.correlation_map.get(correlation_id, [])
        if not entries:
            return {'error': f'No entries found for correlation ID: {correlation_id}'}
        
        # Sort by timestamp
        entries.sort(key=lambda x: x.get('timestamp', ''))
        
        flow_analysis = {
            'correlation_id': correlation_id,
            'total_entries': len(entries),
            'services': list(set(entry['source'] for entry in entries)),
            'timeline': [],
            'agent_orchestration': [],
            'tool_executions': [],
            'errors': []
        }
        
        for entry in entries:
            # Timeline entry
            flow_analysis['timeline'].append({
                'timestamp': entry['timestamp'],
                'service': entry['source'],
                'level': entry['level'],
                'message': entry['message'][:100] + '...' if len(entry['message']) > 100 else entry['message']
            })
            
            # Agent orchestration
            message = entry['message'].lower()
            if any(agent in message for agent in ['agent', 'orchestration', 'workflow']):
                flow_analysis['agent_orchestration'].append({
                    'timestamp': entry['timestamp'],
                    'service': entry['source'],
                    'activity': entry['message']
                })
            
            # Tool executions
            if any(tool in message for tool in ['tool', 'command', 'execution']):
                flow_analysis['tool_executions'].append({
                    'timestamp': entry['timestamp'],
                    'service': entry['source'],
                    'tool_activity': entry['message']
                })
            
            # Errors
            if entry['level'] in ['ERROR', 'FATAL']:
                flow_analysis['errors'].append({
                    'timestamp': entry['timestamp'],
                    'service': entry['source'],
                    'error': entry['message']
                })
        
        return flow_analysis
    
    def generate_summary_report(self) -> Dict:
        """Generate a comprehensive summary report"""
        print("📊 Generating summary report...")
        
        # Sort timeline
        self.request_timeline.sort(key=lambda x: x.get('timestamp', ''))
        
        report = {
            'summary': {
                'total_correlation_ids': len(self.correlation_map),
                'total_log_entries': sum(len(entries) for entries in self.correlation_map.values()),
                'services_involved': list(set(entry['source'] for entries in self.correlation_map.values() for entry in entries)),
                'active_workflows': len(self.agent_flows),
                'tool_executions': len(self.tool_executions)
            },
            'recent_activity': [],
            'top_correlation_ids': [],
            'agent_activity_summary': {},
            'tool_usage_summary': {},
            'error_summary': []
        }
        
        # Recent activity (last 10 entries)
        report['recent_activity'] = [
            {
                'timestamp': entry['timestamp'],
                'service': entry['source'],
                'correlation_id': entry['correlation_id'],
                'message': entry['message'][:80] + '...' if len(entry['message']) > 80 else entry['message']
            }
            for entry in self.request_timeline[-10:]
        ]
        
        # Top correlation IDs by activity
        correlation_activity = [(cid, len(entries)) for cid, entries in self.correlation_map.items()]
        correlation_activity.sort(key=lambda x: x[1], reverse=True)
        report['top_correlation_ids'] = [
            {'correlation_id': cid, 'entry_count': count}
            for cid, count in correlation_activity[:5]
        ]
        
        # Agent activity summary
        for workflow_id, activities in self.agent_flows.items():
            agents_used = set()
            for activity in activities:
                message = activity['message'].lower()
                for agent in ['chatagent', 'planneragent', 'toolsexecutor', 'handoveragent']:
                    if agent in message:
                        agents_used.add(agent)
            
            report['agent_activity_summary'][workflow_id] = {
                'agents_used': list(agents_used),
                'activity_count': len(activities)
            }
        
        # Tool usage summary
        tool_counts = defaultdict(int)
        for workflow_id, executions in self.tool_executions.items():
            for execution in executions:
                message = execution['message'].lower()
                for tool in ['file_system', 'git', 'search', 'code_analysis', 'run_command']:
                    if tool in message:
                        tool_counts[tool] += 1
        
        report['tool_usage_summary'] = dict(tool_counts)
        
        # Error summary
        all_errors = []
        for entries in self.correlation_map.values():
            for entry in entries:
                if entry['level'] in ['ERROR', 'FATAL']:
                    all_errors.append(entry)
        
        report['error_summary'] = [
            {
                'timestamp': error['timestamp'],
                'service': error['source'],
                'correlation_id': error['correlation_id'],
                'error': error['message'][:100] + '...' if len(error['message']) > 100 else error['message']
            }
            for error in all_errors[-5:]  # Last 5 errors
        ]
        
        return report
    
    def print_report(self, report: Dict):
        """Print a formatted report"""
        print("\n" + "="*60)
        print("🔍 GitLab DAP Trace Analysis Report")
        print("="*60)
        
        # Summary
        summary = report['summary']
        print(f"\n📊 Summary:")
        print(f"  Total Correlation IDs: {summary['total_correlation_ids']}")
        print(f"  Total Log Entries: {summary['total_log_entries']}")
        print(f"  Services Involved: {', '.join(summary['services_involved'])}")
        print(f"  Active Workflows: {summary['active_workflows']}")
        print(f"  Tool Executions: {summary['tool_executions']}")
        
        # Top correlation IDs
        print(f"\n🔗 Top Active Correlation IDs:")
        for item in report['top_correlation_ids']:
            print(f"  {item['correlation_id']}: {item['entry_count']} entries")
        
        # Recent activity
        print(f"\n⚡ Recent Activity:")
        for activity in report['recent_activity']:
            print(f"  [{activity['timestamp']}] {activity['service']}: {activity['message']}")
        
        # Agent activity
        if report['agent_activity_summary']:
            print(f"\n🤖 Agent Activity Summary:")
            for workflow_id, summary in list(report['agent_activity_summary'].items())[:3]:
                print(f"  Workflow {workflow_id[:8]}...: {', '.join(summary['agents_used'])} ({summary['activity_count']} activities)")
        
        # Tool usage
        if report['tool_usage_summary']:
            print(f"\n🔧 Tool Usage Summary:")
            for tool, count in report['tool_usage_summary'].items():
                print(f"  {tool}: {count} executions")
        
        # Errors
        if report['error_summary']:
            print(f"\n❌ Recent Errors:")
            for error in report['error_summary']:
                print(f"  [{error['timestamp']}] {error['service']}: {error['error']}")
        
        print("\n" + "="*60)

def main():
    parser = argparse.ArgumentParser(description='GitLab DAP Trace Analyzer')
    parser.add_argument('--trace-dir', default='dap-traces', help='Trace directory path')
    parser.add_argument('--correlation-id', help='Analyze specific correlation ID')
    parser.add_argument('--output', choices=['console', 'json'], default='console', help='Output format')
    parser.add_argument('--server', action='store_true', help='Start web dashboard server')
    parser.add_argument('--port', type=int, default=8080, help='Dashboard server port')

    args = parser.parse_args()

    analyzer = DAPTraceAnalyzer(args.trace_dir)
    analyzer.load_traces()

    if args.server:
        # Start web dashboard server
        server = DAPDashboardServer(analyzer, args.port)
        server.run()
    elif args.correlation_id:
        # Analyze specific correlation ID
        flow_analysis = analyzer.analyze_request_flow(args.correlation_id)
        if args.output == 'json':
            print(json.dumps(flow_analysis, indent=2))
        else:
            print(f"\n🔍 Flow Analysis for Correlation ID: {args.correlation_id}")
            print("="*60)
            print(f"Services: {', '.join(flow_analysis.get('services', []))}")
            print(f"Total Entries: {flow_analysis.get('total_entries', 0)}")

            if flow_analysis.get('timeline'):
                print("\n📅 Timeline:")
                for event in flow_analysis['timeline']:
                    print(f"  [{event['timestamp']}] {event['service']}: {event['message']}")

            if flow_analysis.get('agent_orchestration'):
                print("\n🤖 Agent Orchestration:")
                for event in flow_analysis['agent_orchestration']:
                    print(f"  [{event['timestamp']}] {event['activity']}")

            if flow_analysis.get('tool_executions'):
                print("\n🔧 Tool Executions:")
                for event in flow_analysis['tool_executions']:
                    print(f"  [{event['timestamp']}] {event['tool_activity']}")

            if flow_analysis.get('errors'):
                print("\n❌ Errors:")
                for error in flow_analysis['errors']:
                    print(f"  [{error['timestamp']}] {error['error']}")
    else:
        # Generate summary report
        report = analyzer.generate_summary_report()
        if args.output == 'json':
            print(json.dumps(report, indent=2))
        else:
            analyzer.print_report(report)

class DAPDashboardServer:
    """Web server for real-time DAP debugging dashboard"""

    def __init__(self, analyzer: DAPTraceAnalyzer, port: int = 8080):
        self.analyzer = analyzer
        self.port = port
        self.app = None

    def create_app(self):
        """Create Flask app for dashboard"""
        try:
            from flask import Flask, jsonify, render_template_string, request
            from flask_cors import CORS
        except ImportError:
            print("Flask not installed. Install with: pip install flask flask-cors")
            return None

        app = Flask(__name__)
        CORS(app)

        @app.route('/')
        def dashboard():
            """Serve the dashboard HTML"""
            try:
                with open('dap-debug-dashboard.html', 'r') as f:
                    return f.read()
            except FileNotFoundError:
                return "Dashboard HTML file not found", 404

        @app.route('/api/correlations')
        def get_correlations():
            """Get active correlation IDs"""
            correlations = []
            for cid, entries in self.analyzer.correlation_map.items():
                if entries:
                    services = list(set(entry['source'] for entry in entries))
                    last_activity = max(entry.get('timestamp', '') for entry in entries)
                    correlations.append({
                        'id': cid,
                        'services': services,
                        'entryCount': len(entries),
                        'lastActivity': last_activity
                    })

            # Sort by last activity
            correlations.sort(key=lambda x: x['lastActivity'], reverse=True)
            return jsonify(correlations[:10])  # Return top 10

        @app.route('/api/agents')
        def get_agents():
            """Get agent activity"""
            agents = []
            for workflow_id, activities in self.analyzer.agent_flows.items():
                if activities:
                    latest = activities[-1]
                    agents.append({
                        'workflowId': workflow_id,
                        'name': self._extract_agent_name(latest['message']),
                        'action': latest['message'][:100],
                        'status': self._determine_status(latest),
                        'timestamp': latest.get('timestamp', '')
                    })

            return jsonify(agents[-10:])  # Return latest 10

        @app.route('/api/tools')
        def get_tools():
            """Get tool executions"""
            tools = []
            for workflow_id, executions in self.analyzer.tool_executions.items():
                if executions:
                    latest = executions[-1]
                    tools.append({
                        'workflowId': workflow_id,
                        'name': self._extract_tool_name(latest['message']),
                        'status': self._determine_tool_status(latest),
                        'timestamp': latest.get('timestamp', ''),
                        'duration': self._extract_duration(latest['message'])
                    })

            return jsonify(tools[-10:])  # Return latest 10

        @app.route('/api/flow/<correlation_id>')
        def get_flow(correlation_id):
            """Get request flow for specific correlation ID"""
            flow_analysis = self.analyzer.analyze_request_flow(correlation_id)
            return jsonify(flow_analysis)

        @app.route('/api/logs')
        def get_logs():
            """Get recent logs"""
            level_filter = request.args.get('level', 'all')
            search_term = request.args.get('search', '').lower()
            limit = int(request.args.get('limit', 50))

            logs = []
            for entries in self.analyzer.correlation_map.values():
                logs.extend(entries)

            # Sort by timestamp
            logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

            # Apply filters
            if level_filter != 'all':
                logs = [log for log in logs if log.get('level', '').lower() == level_filter.lower()]

            if search_term:
                logs = [log for log in logs if search_term in log.get('message', '').lower()]

            return jsonify(logs[:limit])

        @app.route('/api/metrics')
        def get_metrics():
            """Get dashboard metrics"""
            return jsonify({
                'requestCount': len(self.analyzer.correlation_map),
                'agentCount': len(self.analyzer.agent_flows),
                'toolCount': len(self.analyzer.tool_executions),
                'totalLogEntries': sum(len(entries) for entries in self.analyzer.correlation_map.values())
            })

        self.app = app
        return app

    def _extract_agent_name(self, message: str) -> str:
        """Extract agent name from log message"""
        message_lower = message.lower()
        for agent in ['chatagent', 'planneragent', 'toolsexecutor', 'handoveragent', 'planterminator']:
            if agent in message_lower:
                return agent.title()
        return 'Unknown'

    def _extract_tool_name(self, message: str) -> str:
        """Extract tool name from log message"""
        message_lower = message.lower()
        for tool in ['file_system', 'git', 'search', 'code_analysis', 'run_command']:
            if tool in message_lower:
                return tool
        return 'unknown_tool'

    def _determine_status(self, entry: Dict) -> str:
        """Determine agent status from log entry"""
        message = entry.get('message', '').lower()
        level = entry.get('level', '').lower()

        if level == 'error':
            return 'error'
        elif 'completed' in message or 'finished' in message:
            return 'completed'
        elif 'started' in message or 'running' in message:
            return 'running'
        else:
            return 'unknown'

    def _determine_tool_status(self, entry: Dict) -> str:
        """Determine tool status from log entry"""
        message = entry.get('message', '').lower()
        level = entry.get('level', '').lower()

        if level == 'error' or 'failed' in message:
            return 'error'
        elif 'success' in message or 'completed' in message:
            return 'success'
        elif 'running' in message or 'executing' in message:
            return 'running'
        else:
            return 'unknown'

    def _extract_duration(self, message: str) -> Optional[float]:
        """Extract duration from log message"""
        import re
        match = re.search(r'(\d+\.?\d*)\s*s(?:ec(?:ond)?s?)?', message.lower())
        if match:
            return float(match.group(1))
        return None

    def run(self):
        """Run the dashboard server"""
        if not self.app:
            self.app = self.create_app()

        if not self.app:
            print("Failed to create Flask app")
            return

        print(f"🌐 Starting DAP Dashboard Server on http://localhost:{self.port}")
        print("📊 Dashboard will be available at http://localhost:{self.port}")

        try:
            self.app.run(host='0.0.0.0', port=self.port, debug=False)
        except Exception as e:
            print(f"Error starting server: {e}")

if __name__ == '__main__':
    main()
