#!/bin/bash

# GitLab DAP Tracing Setup Validation Script
# Comprehensive testing of the enhanced DAP tracing infrastructure

set -e

echo "🧪 GitLab DAP Tracing Setup Validation"
echo "======================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PASSED=0
FAILED=0

print_test() {
    echo -e "${BLUE}Testing: $1${NC}"
}

print_pass() {
    echo -e "${GREEN}✅ PASS: $1${NC}"
    ((PASSED++))
}

print_fail() {
    echo -e "${RED}❌ FAIL: $1${NC}"
    ((FAILED++))
}

print_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
}

# Test 1: Check if all required files exist
test_required_files() {
    print_test "Required files and scripts"
    
    local files=(
        "enhance-dap-tracing.sh"
        "setup-correlation-ids.sh"
        "dap-monitoring-dashboard.sh"
        "DAP_COMPREHENSIVE_TRACING_GUIDE.md"
        "monitor-dap-traces.sh"
        "analyze-dap-traces.sh"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_pass "$file exists"
        else
            print_fail "$file missing"
        fi
    done
}

# Test 2: Check VS Code extension tracing files
test_vscode_extension_files() {
    print_test "VS Code extension tracing files"
    
    local vscode_files=(
        "../gitlab-vscode-extension/src/common/dap_trace_logger.ts"
    )
    
    for file in "${vscode_files[@]}"; do
        if [ -f "$file" ]; then
            print_pass "$(basename "$file") exists"
        else
            print_fail "$(basename "$file") missing"
        fi
    done
}

# Test 3: Check environment configurations
test_environment_configs() {
    print_test "Environment configurations"
    
    # Check AI Gateway .env
    local ai_gateway_env="gitlab-development-kit/gitlab-ai-gateway/.env"
    if [ -f "$ai_gateway_env" ]; then
        if grep -q "LANGCHAIN_TRACING_V2=true" "$ai_gateway_env"; then
            print_pass "AI Gateway LangChain tracing enabled"
        else
            print_fail "AI Gateway LangChain tracing not enabled"
        fi
        
        if grep -q "LANGCHAIN_API_KEY" "$ai_gateway_env"; then
            if grep -q "LANGCHAIN_API_KEY=your_langsmith_api_key_here" "$ai_gateway_env"; then
                print_warning "AI Gateway LangSmith API key needs to be configured"
            else
                print_pass "AI Gateway LangSmith API key configured"
            fi
        else
            print_fail "AI Gateway LangSmith API key not found"
        fi
    else
        print_fail "AI Gateway .env file not found"
    fi
    
    # Check Duo Workflow .env
    local duo_workflow_env="gitlab-development-kit/gitlab-ai-gateway/********************/.env"
    if [ -f "$duo_workflow_env" ]; then
        if grep -q "LANGCHAIN_TRACING_V2=true" "$duo_workflow_env"; then
            print_pass "Duo Workflow LangChain tracing enabled"
        else
            print_fail "Duo Workflow LangChain tracing not enabled"
        fi
        
        if grep -q "GRPC_TRACE" "$duo_workflow_env"; then
            print_pass "Duo Workflow gRPC tracing configured"
        else
            print_fail "Duo Workflow gRPC tracing not configured"
        fi
    else
        print_fail "Duo Workflow .env file not found"
    fi
}

# Test 4: Check service connectivity
test_service_connectivity() {
    print_test "Service connectivity"
    
    # Test GitLab
    if curl -s http://localhost:3000/health >/dev/null 2>&1; then
        print_pass "GitLab Rails responding"
    else
        print_warning "GitLab Rails not responding (may not be started)"
    fi
    
    # Test AI Gateway
    if curl -s http://localhost:5000/health >/dev/null 2>&1; then
        print_pass "AI Gateway responding"
    else
        print_warning "AI Gateway not responding (may not be started)"
    fi
    
    # Test Duo Workflow Service
    if command -v grpcurl >/dev/null 2>&1; then
        if grpcurl -plaintext localhost:50052 list >/dev/null 2>&1; then
            print_pass "Duo Workflow Service responding"
        else
            print_warning "Duo Workflow Service not responding (may not be started)"
        fi
    else
        print_warning "grpcurl not installed - cannot test Duo Workflow Service"
    fi
}

# Test 5: Check log file accessibility
test_log_files() {
    print_test "Log file accessibility"
    
    local log_files=(
        "../gdk/gitlab/log/development.log:GitLab Rails"
        "../gdk/log/gitlab-ai-gateway/gateway_debug.log:AI Gateway"
        "../gdk/log/duo-workflow-service/current:Duo Workflow"
    )
    
    for log_entry in "${log_files[@]}"; do
        IFS=':' read -r log_file service_name <<< "$log_entry"
        if [ -f "$log_file" ]; then
            print_pass "$service_name log file accessible"
        else
            print_warning "$service_name log file not found (service may not be running)"
        fi
    done
}

# Test 6: Check script permissions
test_script_permissions() {
    print_test "Script permissions"
    
    local scripts=(
        "enhance-dap-tracing.sh"
        "setup-correlation-ids.sh"
        "dap-monitoring-dashboard.sh"
        "monitor-dap-traces.sh"
        "analyze-dap-traces.sh"
        "test-correlation-ids.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            if [ -x "$script" ]; then
                print_pass "$script is executable"
            else
                print_fail "$script is not executable"
            fi
        fi
    done
}

# Test 7: Check dependencies
test_dependencies() {
    print_test "Required dependencies"
    
    local deps=(
        "curl:HTTP client"
        "jq:JSON processor"
        "tail:Log monitoring"
        "grep:Text search"
    )
    
    for dep_entry in "${deps[@]}"; do
        IFS=':' read -r cmd desc <<< "$dep_entry"
        if command -v "$cmd" >/dev/null 2>&1; then
            print_pass "$desc ($cmd) available"
        else
            print_fail "$desc ($cmd) not found"
        fi
    done
    
    # Optional dependencies
    if command -v grpcurl >/dev/null 2>&1; then
        print_pass "grpcurl available (optional)"
    else
        print_warning "grpcurl not found (optional - for gRPC testing)"
    fi
    
    if command -v multitail >/dev/null 2>&1; then
        print_pass "multitail available (optional)"
    else
        print_warning "multitail not found (optional - for enhanced log monitoring)"
    fi
}

# Test 8: Validate configuration syntax
test_config_syntax() {
    print_test "Configuration file syntax"
    
    # Test .env files for basic syntax
    local env_files=(
        "gitlab-development-kit/gitlab-ai-gateway/.env"
        "gitlab-development-kit/gitlab-ai-gateway/********************/.env"
    )
    
    for env_file in "${env_files[@]}"; do
        if [ -f "$env_file" ]; then
            # Basic syntax check - look for malformed lines
            if grep -q "^[^#]*=" "$env_file" && ! grep -q "^[^#]*=.*=" "$env_file"; then
                print_pass "$(basename "$env_file") syntax looks good"
            else
                print_warning "$(basename "$env_file") may have syntax issues"
            fi
        fi
    done
}

# Test 9: Check VS Code extension integration
test_vscode_integration() {
    print_test "VS Code extension integration"
    
    local vscode_file="../gitlab-vscode-extension/src/common/dap_trace_logger.ts"
    if [ -f "$vscode_file" ]; then
        if grep -q "generateCorrelationId" "$vscode_file"; then
            print_pass "VS Code extension correlation ID generation implemented"
        else
            print_fail "VS Code extension correlation ID generation not found"
        fi
        
        if grep -q "logDAPRequest" "$vscode_file"; then
            print_pass "VS Code extension DAP request logging implemented"
        else
            print_fail "VS Code extension DAP request logging not found"
        fi
    else
        print_fail "VS Code extension tracing file not found"
    fi
}

# Test 10: End-to-end test preparation
test_e2e_preparation() {
    print_test "End-to-end test preparation"
    
    if [ -f "test-correlation-ids.sh" ] && [ -x "test-correlation-ids.sh" ]; then
        print_pass "Correlation ID test script ready"
    else
        print_fail "Correlation ID test script not ready"
    fi
    
    if [ -f "monitor-correlation-ids.sh" ] && [ -x "monitor-correlation-ids.sh" ]; then
        print_pass "Correlation ID monitoring script ready"
    else
        print_fail "Correlation ID monitoring script not ready"
    fi
}

# Run all tests
run_all_tests() {
    echo ""
    echo "Running comprehensive DAP tracing setup validation..."
    echo ""
    
    test_required_files
    echo ""
    test_vscode_extension_files
    echo ""
    test_environment_configs
    echo ""
    test_service_connectivity
    echo ""
    test_log_files
    echo ""
    test_script_permissions
    echo ""
    test_dependencies
    echo ""
    test_config_syntax
    echo ""
    test_vscode_integration
    echo ""
    test_e2e_preparation
    echo ""
}

# Print summary
print_summary() {
    echo "======================================"
    echo -e "${BLUE}Test Summary${NC}"
    echo "======================================"
    echo -e "Tests passed: ${GREEN}$PASSED${NC}"
    echo -e "Tests failed: ${RED}$FAILED${NC}"
    echo ""
    
    if [ $FAILED -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed! Your DAP tracing setup is ready.${NC}"
        echo ""
        echo "Next steps:"
        echo "1. Configure your LangSmith API key if not done already"
        echo "2. Start services: gdk restart"
        echo "3. Test the setup: ./test-correlation-ids.sh"
        echo "4. Start monitoring: ./dap-monitoring-dashboard.sh"
    else
        echo -e "${YELLOW}⚠️  Some tests failed. Please review the issues above.${NC}"
        echo ""
        echo "Common fixes:"
        echo "1. Run: ./enhance-dap-tracing.sh"
        echo "2. Run: ./setup-correlation-ids.sh"
        echo "3. Configure LangSmith API keys"
        echo "4. Start services: gdk start"
    fi
    
    echo ""
    echo "For detailed setup instructions, see:"
    echo "  DAP_COMPREHENSIVE_TRACING_GUIDE.md"
}

# Main execution
run_all_tests
print_summary
