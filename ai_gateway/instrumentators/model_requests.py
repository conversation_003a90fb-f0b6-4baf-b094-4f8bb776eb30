import time
from contextlib import contextmanager
from contextvars import ContextV<PERSON>
from typing import List, Optional

import structlog
from gitlab_cloud_connector import GitLabUnitPrimitive
from langchain_core.messages.ai import UsageMetadata
from prometheus_client import Counter, Gauge, Histogram

from ai_gateway.api.feature_category import current_feature_category
from ai_gateway.config import ModelLimits
from ai_gateway.tracking.errors import log_exception

METRIC_LABELS = ["model_engine", "model_name"]
INFERENCE_DETAILS = METRIC_LABELS + [
    "error",
    "streaming",
    "feature_category",
    "unit_primitive",
]

INFERENCE_IN_FLIGHT_GAUGE = Gauge(
    "model_inferences_in_flight",
    "The number of in flight inferences running",
    METRIC_LABELS,
)

MAX_CONCURRENT_MODEL_INFERENCES = Gauge(
    "model_inferences_max_concurrent",
    "The maximum number of inferences we can run concurrently on a model",
    METRIC_LABELS,
)
MAX_MODEL_INPUT_TOKENS = Gauge(
    "model_max_input_tokens",
    "The per-minute limit on input tokens for a model",
    METRIC_LABELS,
)
MAX_MODEL_OUTPUT_TOKENS = Gauge(
    "model_max_output_tokens",
    "The per-minute limit on output tokens for a model",
    METRIC_LABELS,
)

# The counter and histogram from `instrumentators/base.py` can be removed once
# the SLIs stop using these. Then all requests, not just the code-suggestion ones
# will be instrumented
# We'll remove this as part of https://gitlab.com/gitlab-org/modelops/applied-ml/code-suggestions/ai-assist/-/issues/441
INFERENCE_COUNTER = Counter(
    "model_inferences_total",
    "The total number of inferences on a model with a label",
    INFERENCE_DETAILS,
)

INFERENCE_DURATION_S = Histogram(
    "inference_request_duration_seconds",
    "Duration of the inference request in seconds",
    INFERENCE_DETAILS,
    buckets=(0.5, 1, 2.5, 5, 10, 30, 60),
)

INFERENCE_INPUT_TOKENS = Counter(
    "inference_input_tokens",
    "The total number of input tokens processed by the model",
    INFERENCE_DETAILS,
)

INFERENCE_OUTPUT_TOKENS = Counter(
    "inference_output_tokens",
    "The total number of output tokens generated by the model",
    INFERENCE_DETAILS,
)

type TokenUsage = dict[str, dict[str, int]]
token_usage: ContextVar[TokenUsage | None] = ContextVar("token_usage", default=None)

logger = structlog.get_logger()


def _update_token_usage(model: str, usage: UsageMetadata) -> None:
    current_usage = token_usage.get() or {}
    current_usage.setdefault(model, {"input_tokens": 0, "output_tokens": 0})
    current_usage[model]["input_tokens"] += usage["input_tokens"]
    current_usage[model]["output_tokens"] += usage["output_tokens"]

    token_usage.set(current_usage)


def get_token_usage() -> TokenUsage | None:
    current_usage = token_usage.get()

    # Reset the usage so multiple requests don't return the same values
    token_usage.set(None)

    return current_usage


class ModelRequestInstrumentator:
    class WatchContainer:
        def __init__(
            self,
            labels: dict[str, str],
            limits: Optional[ModelLimits],
            streaming: bool,
            unit_primitives: Optional[List[GitLabUnitPrimitive]] = None,
        ):
            self.labels = labels
            self.limits = limits
            self.error = False
            self.streaming = streaming
            self.start_time = None
            self.unit_primitives = unit_primitives

        def start(self):
            """Register the start of the inference request.

            Sets the start time to be used for duration calculations when `finish()` is called.
            """
            self.start_time = time.perf_counter()

            if self.limits:
                if "concurrency" in self.limits:
                    MAX_CONCURRENT_MODEL_INFERENCES.labels(**self.labels).set(
                        self.limits["concurrency"]
                    )
                if "input_tokens" in self.limits:
                    MAX_MODEL_INPUT_TOKENS.labels(**self.labels).set(
                        self.limits["input_tokens"]
                    )
                if "output_tokens" in self.limits:
                    MAX_MODEL_OUTPUT_TOKENS.labels(**self.labels).set(
                        self.limits["output_tokens"]
                    )

            INFERENCE_IN_FLIGHT_GAUGE.labels(**self.labels).inc()

        def register_error(self):
            self.error = True

        def register_token_usage(self, model: str, usage: UsageMetadata):
            token_usage_labels = {**self._detail_labels(), "model_name": model}

            _update_token_usage(model, usage)

            INFERENCE_INPUT_TOKENS.labels(**token_usage_labels).inc(
                usage["input_tokens"]
            )
            INFERENCE_OUTPUT_TOKENS.labels(**token_usage_labels).inc(
                usage["output_tokens"]
            )

        def finish(self):
            """Register the end of the inference request.

            Duration is calculated from the start time set by `start()`.
            """
            INFERENCE_IN_FLIGHT_GAUGE.labels(**self.labels).dec()

            duration = time.perf_counter() - self.start_time
            logger.info("Request to LLM complete", source=__name__, duration=duration)

            detail_labels = self._detail_labels()

            INFERENCE_COUNTER.labels(**detail_labels).inc()
            INFERENCE_DURATION_S.labels(**detail_labels).observe(duration)

        async def afinish(self):
            self.finish()

        def _detail_labels(self) -> dict[str, str]:
            unit_primitive = (
                self.unit_primitives[0].value
                if self.unit_primitives and len(self.unit_primitives) > 0
                else "unknown"
            )
            detail_labels = {
                "error": "yes" if self.error else "no",
                "streaming": "yes" if self.streaming else "no",
                "feature_category": current_feature_category(),
                "unit_primitive": unit_primitive,
            }
            return {**self.labels, **detail_labels}

    def __init__(
        self,
        model_engine: str,
        model_name: str,
        limits: Optional[ModelLimits],
    ):
        self.labels = {"model_engine": model_engine, "model_name": model_name}
        self.limits = limits

    @contextmanager
    def watch(self, stream=False, unit_primitives=None):
        watcher = ModelRequestInstrumentator.WatchContainer(
            labels=self.labels,
            limits=self.limits,
            streaming=stream,
            unit_primitives=unit_primitives,
        )
        watcher.start()
        try:
            yield watcher
        except Exception as ex:
            log_exception(ex, self.labels)
            watcher.register_error()
            watcher.finish()
            raise

        if not stream:
            watcher.finish()
