X_GITLAB_REALM_HEADER = "X-Gitlab-Realm"
X_GITLAB_INSTANCE_ID_HEADER = "********************"
X_GITLAB_GLOBAL_USER_ID_HEADER = "X-Gitlab-Global-User-Id"
X_GITLAB_TEAM_MEMBER_HEADER = "X-*********************"
X_GITLAB_HOST_NAME_HEADER = "X-Gitlab-Host-Name"
X_GITLAB_VERSION_HEADER = "X-Gitlab-Version"
X_GITLAB_SAAS_DUO_PRO_NAMESPACE_IDS_HEADER = "**********************Namespace-Ids"
X_GITLAB_FEATURE_ENABLED_BY_NAMESPACE_IDS_HEADER = (
    "X-Gitlab-Feature-Enabled-By-Namespace-Ids"
)
X_GITLAB_FEATURE_ENABLEMENT_TYPE_HEADER = "X-Gitlab-Feature-Enablement-Type"
X_GITLAB_MODEL_GATEWAY_REQUEST_SENT_AT = "********************Start"
X_GITLAB_LANGUAGE_SERVER_VERSION = "X-Gitlab-Language-Server-Version"
X_GITLAB_MODEL_PROMPT_CACHE_ENABLED = "X-Gitlab-Model-Prompt-Cache-Enabled"
X_GITLAB_ENABLED_FEATURE_FLAGS = "x-gitlab-enabled-feature-flags"
X_GITLAB_CLIENT_TYPE = "X-Gitlab-Client-Type"
X_GITLAB_CLIENT_VERSION = "X-Gitlab-Client-Version"
X_GITLAB_CLIENT_NAME = "X-Gitlab-Client-Name"
X_GITLAB_INTERFACE = "X-Gitlab-Interface"
