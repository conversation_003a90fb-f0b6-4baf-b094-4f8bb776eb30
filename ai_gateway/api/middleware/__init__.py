from .authentication import MiddlewareAuthentication
from .base import AccessLogMiddleware
from .distributed_trace import DistributedTraceMiddleware
from .feature_flag import FeatureFlagMiddleware
from .headers import (
    X_GITLAB_CLIENT_NAME,
    X_GITLAB_CLIENT_TYPE,
    X_GITLAB_CLIENT_VERSION,
    X_GITLAB_FEATURE_ENABLED_BY_NAMESPACE_IDS_HEADER,
    X_GITLAB_FEATURE_ENABLEMENT_TYPE_HEADER,
    X_GITLAB_GLOBAL_USER_ID_HEADER,
    X_GITLAB_HOST_NAME_HEADER,
    X_GITLAB_INSTANCE_ID_HEADER,
    X_GITLAB_INTERFACE,
    X_GITLAB_LANGUAGE_SERVER_VERSION,
    X_GITLAB_MODEL_GATEWAY_REQUEST_SENT_AT,
    X_GITLAB_REALM_HEADER,
    X_GIT<PERSON><PERSON>_SAAS_DUO_PRO_NAMESPACE_IDS_HEADER,
    X_GITLAB_TEAM_MEMBER_HEADER,
    X_GITLAB_VERSION_HEADER,
)
from .internal_event import InternalEventMiddleware
from .model_config import ModelConfigMiddleware

__all__ = [
    "AccessLogMiddleware",
    "DistributedTraceMiddleware",
    "FeatureFlagMiddleware",
    "InternalEventMiddleware",
    "MiddlewareAuthentication",
    "ModelConfigMiddleware",
    "X_GITLAB_CLIENT_NAME",
    "X_GITLAB_CLIENT_TYPE",
    "X_GITLAB_CLIENT_VERSION",
    "X_GITLAB_FEATURE_ENABLED_BY_NAMESPACE_IDS_HEADER",
    "X_GITLAB_FEATURE_ENABLEMENT_TYPE_HEADER",
    "X_GITLAB_GLOBAL_USER_ID_HEADER",
    "X_GITLAB_HOST_NAME_HEADER",
    "X_GITLAB_INSTANCE_ID_HEADER",
    "X_GITLAB_INTERFACE",
    "X_GITLAB_LANGUAGE_SERVER_VERSION",
    "X_GITLAB_MODEL_GATEWAY_REQUEST_SENT_AT",
    "X_GITLAB_REALM_HEADER",
    "X_GITLAB_SAAS_DUO_PRO_NAMESPACE_IDS_HEADER",
    "X_GITLAB_TEAM_MEMBER_HEADER",
    "X_GITLAB_VERSION_HEADER",
]
