#!/bin/bash

# GitLab DAP Enhanced Tracing Setup Script
# This script enhances the tracing infrastructure for complete DAP observability

set -e

echo "🔍 GitLab DAP Enhanced Tracing Setup"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if LangSmith API key is configured
check_langsmith_config() {
    print_info "Checking LangSmith configuration..."
    
    AI_GATEWAY_ENV="gitlab-development-kit/gitlab-ai-gateway/.env"
    DUO_WORKFLOW_ENV="gitlab-development-kit/gitlab-ai-gateway/********************/.env"
    
    if grep -q "LANGCHAIN_API_KEY=your_langsmith_api_key_here" "$AI_GATEWAY_ENV" 2>/dev/null; then
        print_warning "LangSmith API key not configured in AI Gateway"
        echo "Please update $AI_GATEWAY_ENV with your actual LangSmith API key"
        echo "Get your API key from: https://smith.langchain.com/"
        return 1
    fi
    
    if grep -q "LANGCHAIN_API_KEY=your_langsmith_api_key_here" "$DUO_WORKFLOW_ENV" 2>/dev/null; then
        print_warning "LangSmith API key not configured in Duo Workflow Service"
        echo "Please update $DUO_WORKFLOW_ENV with your actual LangSmith API key"
        return 1
    fi
    
    print_status "LangSmith configuration looks good"
    return 0
}

# Enhanced gRPC tracing configuration
enhance_grpc_tracing() {
    print_info "Enhancing gRPC tracing configuration..."
    
    DUO_WORKFLOW_ENV="gitlab-development-kit/gitlab-ai-gateway/********************/.env"
    
    # Check if enhanced gRPC tracing is already configured
    if ! grep -q "GRPC_TRACE=.*channel" "$DUO_WORKFLOW_ENV" 2>/dev/null; then
        print_info "Adding enhanced gRPC tracing configuration..."
        
        # Backup the original file
        cp "$DUO_WORKFLOW_ENV" "$DUO_WORKFLOW_ENV.backup"
        
        # Update gRPC tracing to include more detailed information
        sed -i.tmp 's/GRPC_TRACE=.*/GRPC_TRACE=http_keepalive,call_error,connectivity_state,channel,subchannel,http,transport_security/' "$DUO_WORKFLOW_ENV"
        rm "$DUO_WORKFLOW_ENV.tmp"
        
        # Add gRPC verbosity for detailed debugging
        if ! grep -q "GRPC_VERBOSITY" "$DUO_WORKFLOW_ENV"; then
            echo "" >> "$DUO_WORKFLOW_ENV"
            echo "# Enhanced gRPC debugging" >> "$DUO_WORKFLOW_ENV"
            echo "GRPC_VERBOSITY=DEBUG" >> "$DUO_WORKFLOW_ENV"
        fi
        
        print_status "Enhanced gRPC tracing configuration added"
    else
        print_status "Enhanced gRPC tracing already configured"
    fi
}

# Configure OpenTelemetry integration
configure_opentelemetry() {
    print_info "Configuring OpenTelemetry integration..."

    # Add OTEL configuration to AI Gateway
    AI_GATEWAY_ENV="gitlab-development-kit/gitlab-ai-gateway/.env"

    if ! grep -q "OTEL_" "$AI_GATEWAY_ENV" 2>/dev/null; then
        print_info "Adding OpenTelemetry configuration to AI Gateway..."

        cat >> "$AI_GATEWAY_ENV" << 'EOF'

# OpenTelemetry Configuration
OTEL_SERVICE_NAME=ai-gateway
OTEL_SERVICE_VERSION=1.0.0
OTEL_RESOURCE_ATTRIBUTES=service.name=ai-gateway,service.version=1.0.0,deployment.environment=development
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
OTEL_TRACES_EXPORTER=otlp
OTEL_METRICS_EXPORTER=otlp
OTEL_LOGS_EXPORTER=otlp
EOF
        print_status "OpenTelemetry configuration added to AI Gateway"
    else
        print_status "OpenTelemetry already configured in AI Gateway"
    fi

    # Add OTEL configuration to Duo Workflow Service
    DUO_WORKFLOW_ENV="gitlab-development-kit/gitlab-ai-gateway/********************/.env"

    if ! grep -q "OTEL_" "$DUO_WORKFLOW_ENV" 2>/dev/null; then
        print_info "Adding OpenTelemetry configuration to Duo Workflow Service..."

        cat >> "$DUO_WORKFLOW_ENV" << 'EOF'

# OpenTelemetry Configuration
OTEL_SERVICE_NAME=duo-workflow-service
OTEL_SERVICE_VERSION=1.0.0
OTEL_RESOURCE_ATTRIBUTES=service.name=duo-workflow-service,service.version=1.0.0,deployment.environment=development
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
OTEL_TRACES_EXPORTER=otlp
OTEL_METRICS_EXPORTER=otlp
OTEL_LOGS_EXPORTER=otlp
EOF
        print_status "OpenTelemetry configuration added to Duo Workflow Service"
    else
        print_status "OpenTelemetry already configured in Duo Workflow Service"
    fi
}

# Create enhanced monitoring script
create_enhanced_monitoring() {
    print_info "Creating enhanced monitoring script..."
    
    cat > monitor-dap-traces.sh << 'EOF'
#!/bin/bash

# Enhanced DAP Tracing Monitor
# Monitors all DAP-related logs with structured filtering and correlation

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Log file paths
GITLAB_LOG="../gdk/gitlab/log/development.log"
AI_GATEWAY_LOG="../gdk/log/gitlab-ai-gateway/gateway_debug.log"
DUO_WORKFLOW_LOG="../gdk/log/duo-workflow-service/current"
VSCODE_LOG="logs/gitlab-ai-logs.log"

print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}  GitLab DAP Trace Monitor${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
}

print_section() {
    echo -e "${BLUE}--- $1 ---${NC}"
}

# Function to extract correlation IDs and trace important events
monitor_logs() {
    print_header
    
    print_section "Starting Enhanced DAP Monitoring"
    echo "Monitoring the following logs:"
    echo "  • GitLab Rails: $GITLAB_LOG"
    echo "  • AI Gateway: $AI_GATEWAY_LOG" 
    echo "  • Duo Workflow: $DUO_WORKFLOW_LOG"
    echo "  • VS Code Extension: $VSCODE_LOG"
    echo ""
    echo "Looking for:"
    echo "  • DAP requests and responses"
    echo "  • Agent orchestration events"
    echo "  • Tool selection and execution"
    echo "  • Correlation ID propagation"
    echo "  • LangSmith trace links"
    echo ""
    echo -e "${YELLOW}Press Ctrl+C to stop monitoring${NC}"
    echo ""
    
    # Use multitail if available, otherwise fall back to tail
    if command -v multitail >/dev/null 2>&1; then
        multitail \
            -ci green -I "$GITLAB_LOG" \
            -ci blue -I "$AI_GATEWAY_LOG" \
            -ci yellow -I "$DUO_WORKFLOW_LOG" \
            -ci cyan -I "$VSCODE_LOG" \
            --follow-all
    else
        # Fallback to regular tail with labels
        tail -f "$GITLAB_LOG" "$AI_GATEWAY_LOG" "$DUO_WORKFLOW_LOG" "$VSCODE_LOG" 2>/dev/null | \
        while IFS= read -r line; do
            case "$line" in
                *"==> $GITLAB_LOG <=="*)
                    echo -e "${GREEN}[GitLab Rails]${NC}"
                    ;;
                *"==> $AI_GATEWAY_LOG <=="*)
                    echo -e "${BLUE}[AI Gateway]${NC}"
                    ;;
                *"==> $DUO_WORKFLOW_LOG <=="*)
                    echo -e "${YELLOW}[Duo Workflow]${NC}"
                    ;;
                *"==> $VSCODE_LOG <=="*)
                    echo -e "${CYAN}[VS Code Extension]${NC}"
                    ;;
                *correlation_id*)
                    echo -e "${CYAN}🔗 $line${NC}"
                    ;;
                *workflow_id*)
                    echo -e "${YELLOW}🔄 $line${NC}"
                    ;;
                *tool*)
                    echo -e "${GREEN}🔧 $line${NC}"
                    ;;
                *agent*)
                    echo -e "${BLUE}🤖 $line${NC}"
                    ;;
                *error*|*Error*|*ERROR*)
                    echo -e "${RED}❌ $line${NC}"
                    ;;
                *)
                    echo "$line"
                    ;;
            esac
        done
    fi
}

# Check if log files exist
check_logs() {
    local missing_logs=()
    
    [ ! -f "$GITLAB_LOG" ] && missing_logs+=("GitLab Rails: $GITLAB_LOG")
    [ ! -f "$AI_GATEWAY_LOG" ] && missing_logs+=("AI Gateway: $AI_GATEWAY_LOG")
    [ ! -f "$DUO_WORKFLOW_LOG" ] && missing_logs+=("Duo Workflow: $DUO_WORKFLOW_LOG")
    [ ! -f "$VSCODE_LOG" ] && missing_logs+=("VS Code Extension: $VSCODE_LOG")
    
    if [ ${#missing_logs[@]} -gt 0 ]; then
        echo -e "${RED}Missing log files:${NC}"
        for log in "${missing_logs[@]}"; do
            echo -e "  ${RED}✗${NC} $log"
        done
        echo ""
        echo "Make sure all services are running:"
        echo "  gdk status"
        echo "  ./test-gitlab-extension.sh status"
        exit 1
    fi
}

# Main execution
case "${1:-monitor}" in
    "monitor")
        check_logs
        monitor_logs
        ;;
    "check")
        check_logs
        echo -e "${GREEN}All log files are accessible${NC}"
        ;;
    *)
        echo "Usage: $0 [monitor|check]"
        echo "  monitor: Start monitoring DAP traces (default)"
        echo "  check: Check if all log files are accessible"
        exit 1
        ;;
esac
EOF

    chmod +x monitor-dap-traces.sh
    print_status "Enhanced monitoring script created: monitor-dap-traces.sh"
}

# Create trace analysis script
create_trace_analysis() {
    print_info "Creating trace analysis script..."
    
    cat > analyze-dap-traces.sh << 'EOF'
#!/bin/bash

# DAP Trace Analysis Script
# Analyzes DAP traces for debugging and performance insights

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Log file paths
GITLAB_LOG="../gdk/gitlab/log/development.log"
AI_GATEWAY_LOG="../gdk/log/gitlab-ai-gateway/gateway_debug.log"
DUO_WORKFLOW_LOG="../gdk/log/duo-workflow-service/current"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  DAP Trace Analysis${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

analyze_correlation_ids() {
    echo -e "${GREEN}🔗 Correlation ID Analysis${NC}"
    echo "Recent correlation IDs found:"
    
    # Extract correlation IDs from the last hour
    find_recent_correlation_ids() {
        local log_file="$1"
        local service_name="$2"
        
        if [ -f "$log_file" ]; then
            tail -n 1000 "$log_file" | grep -o 'correlation_id[":=][^",:}]*' | \
            sed 's/correlation_id[":=]//' | sort | uniq | head -5 | \
            while read -r cid; do
                echo -e "  ${YELLOW}$service_name:${NC} $cid"
            done
        fi
    }
    
    find_recent_correlation_ids "$GITLAB_LOG" "GitLab"
    find_recent_correlation_ids "$AI_GATEWAY_LOG" "AI Gateway"
    find_recent_correlation_ids "$DUO_WORKFLOW_LOG" "Duo Workflow"
    echo ""
}

analyze_workflow_performance() {
    echo -e "${GREEN}⏱️  Workflow Performance Analysis${NC}"
    
    if [ -f "$DUO_WORKFLOW_LOG" ]; then
        echo "Recent workflow durations:"
        tail -n 500 "$DUO_WORKFLOW_LOG" | grep -i "workflow.*duration\|workflow.*seconds" | tail -5
        echo ""
        
        echo "Tool execution summary:"
        tail -n 500 "$DUO_WORKFLOW_LOG" | grep -i "tool.*executed\|executing.*tool" | tail -5
        echo ""
    else
        echo -e "${RED}Duo Workflow log not found${NC}"
    fi
}

analyze_errors() {
    echo -e "${RED}❌ Error Analysis${NC}"
    echo "Recent errors across all services:"
    
    analyze_service_errors() {
        local log_file="$1"
        local service_name="$2"
        
        if [ -f "$log_file" ]; then
            local error_count=$(tail -n 500 "$log_file" | grep -i "error\|exception" | wc -l)
            echo -e "  ${service_name}: ${error_count} errors in last 500 lines"
            
            if [ "$error_count" -gt 0 ]; then
                echo "    Recent errors:"
                tail -n 500 "$log_file" | grep -i "error\|exception" | tail -3 | \
                while IFS= read -r line; do
                    echo -e "    ${RED}→${NC} $(echo "$line" | cut -c1-100)..."
                done
            fi
        fi
    }
    
    analyze_service_errors "$GITLAB_LOG" "GitLab Rails"
    analyze_service_errors "$AI_GATEWAY_LOG" "AI Gateway"
    analyze_service_errors "$DUO_WORKFLOW_LOG" "Duo Workflow"
    echo ""
}

trace_request_flow() {
    local correlation_id="$1"
    
    if [ -z "$correlation_id" ]; then
        echo "Usage: $0 trace <correlation_id>"
        echo ""
        echo "Available correlation IDs:"
        analyze_correlation_ids
        return 1
    fi
    
    echo -e "${BLUE}🔍 Tracing request flow for correlation ID: ${YELLOW}$correlation_id${NC}"
    echo ""
    
    trace_in_log() {
        local log_file="$1"
        local service_name="$2"
        
        if [ -f "$log_file" ]; then
            local matches=$(grep "$correlation_id" "$log_file" | wc -l)
            if [ "$matches" -gt 0 ]; then
                echo -e "${GREEN}$service_name ($matches matches):${NC}"
                grep "$correlation_id" "$log_file" | while IFS= read -r line; do
                    echo -e "  ${YELLOW}→${NC} $(echo "$line" | cut -c1-120)..."
                done
                echo ""
            fi
        fi
    }
    
    trace_in_log "$GITLAB_LOG" "GitLab Rails"
    trace_in_log "$AI_GATEWAY_LOG" "AI Gateway"
    trace_in_log "$DUO_WORKFLOW_LOG" "Duo Workflow"
}

# Main execution
case "${1:-summary}" in
    "summary")
        print_header
        analyze_correlation_ids
        analyze_workflow_performance
        analyze_errors
        ;;
    "trace")
        trace_request_flow "$2"
        ;;
    "errors")
        print_header
        analyze_errors
        ;;
    "performance")
        print_header
        analyze_workflow_performance
        ;;
    *)
        echo "Usage: $0 [summary|trace <correlation_id>|errors|performance]"
        echo "  summary: Show overall trace analysis (default)"
        echo "  trace <id>: Trace a specific correlation ID across services"
        echo "  errors: Show error analysis"
        echo "  performance: Show performance analysis"
        exit 1
        ;;
esac
EOF

    chmod +x analyze-dap-traces.sh
    print_status "Trace analysis script created: analyze-dap-traces.sh"
}

# Main execution
main() {
    echo ""
    print_info "Starting DAP tracing enhancement..."
    echo ""
    
    # Check LangSmith configuration
    if ! check_langsmith_config; then
        print_error "Please configure LangSmith API key before proceeding"
        exit 1
    fi
    
    # Enhance gRPC tracing
    enhance_grpc_tracing

    # Configure OpenTelemetry
    configure_opentelemetry

    # Create monitoring scripts
    create_enhanced_monitoring
    create_trace_analysis
    
    echo ""
    print_status "DAP tracing enhancement complete!"
    echo ""
    print_info "Next steps:"
    echo "1. Configure your LangSmith API key in the .env files"
    echo "2. Restart your services: gdk restart"
    echo "3. Start monitoring: ./monitor-dap-traces.sh"
    echo "4. Analyze traces: ./analyze-dap-traces.sh"
    echo ""
    print_info "For VS Code extension debugging:"
    echo "1. Open the extension project: cd ../gitlab-vscode-extension && code ."
    echo "2. Press F5 to launch the extension in debug mode"
    echo "3. Check 'GitLab Workflow' output panel for DAP logs"
    echo ""
}

main "$@"
