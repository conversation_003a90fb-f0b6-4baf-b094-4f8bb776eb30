# GitLab DAP Comprehensive Tracing Guide

This guide provides complete end-to-end tracing for GitLab Duo Agent Platform (DAP) across all components.

## 🎯 Overview

The DAP tracing infrastructure now includes:

1. **LangSmith Integration** - Agent orchestration and LLM call tracing
2. **Enhanced VS Code Extension Logging** - Client-side request/response tracing with correlation IDs
3. **OpenTelemetry Integration** - Distributed tracing across all services
4. **Structured Logging** - Consistent log format with correlation ID propagation
5. **Real-time Monitoring** - Live trace analysis and debugging tools

## 🚀 Quick Setup

### 1. Configure <PERSON><PERSON>mith (Required)

```bash
# Get your LangSmith API key from https://smith.langchain.com/
# Edit these files and replace 'your_langsmith_api_key_here' with your actual key:

# AI Gateway
vim gitlab-development-kit/gitlab-ai-gateway/.env

# Duo Workflow Service  
vim gitlab-development-kit/gitlab-ai-gateway/********************/.env
```

### 2. Run Enhanced Tracing Setup

```bash
./enhance-dap-tracing.sh
```

### 3. Restart Services

```bash
gdk restart
```

### 4. Start Monitoring

```bash
./monitor-dap-traces.sh
```

## 📊 Tracing Components

### LangSmith Tracing
- **Purpose**: Agent orchestration, tool selection, LLM interactions
- **Location**: https://smith.langchain.com/
- **Project**: `duo-workflow-local-debug`
- **What you'll see**:
  - Complete agent workflow execution
  - Tool selection reasoning
  - LLM request/response pairs
  - Error traces with full context

### VS Code Extension Tracing
- **Purpose**: Client-side DAP interactions
- **Location**: VS Code Output Panel → "GitLab Workflow"
- **Features**:
  - Correlation ID generation for request tracking
  - Language server communication logging
  - Terminal command execution tracing
  - Connection state monitoring

### OpenTelemetry Tracing
- **Purpose**: Distributed tracing across services
- **Endpoint**: http://localhost:4318
- **Storage**: ClickHouse (via GitLab Observability Backend)
- **Services Traced**:
  - AI Gateway (`ai-gateway`)
  - Duo Workflow Service (`duo-workflow-service`)

### Structured Logging
- **AI Gateway**: `gdk/log/gitlab-ai-gateway/gateway_debug.log`
- **Duo Workflow**: `gdk/log/duo-workflow-service/current`
- **GitLab Rails**: `gdk/gitlab/log/development.log`
- **VS Code Extension**: VS Code Output Panel

## 🔍 Trace Analysis

### Real-time Monitoring

```bash
# Start comprehensive monitoring
./monitor-dap-traces.sh

# Check log file accessibility
./monitor-dap-traces.sh check
```

### Trace Analysis

```bash
# Overall trace summary
./analyze-dap-traces.sh

# Trace specific correlation ID
./analyze-dap-traces.sh trace <correlation_id>

# Error analysis
./analyze-dap-traces.sh errors

# Performance analysis
./analyze-dap-traces.sh performance
```

## 🔗 Correlation ID Flow

Each DAP request generates a unique correlation ID that flows through all services:

1. **VS Code Extension** generates: `vscode-20240101T120000-0001`
2. **Language Server** forwards with same ID
3. **GitLab Rails** propagates to AI Gateway
4. **AI Gateway** includes in LangSmith traces
5. **Duo Workflow Service** uses for all operations

## 🎯 What to Look For

### Successful DAP Request Flow

1. **VS Code Extension**:
   ```
   🚀 [DAP-REQUEST] vscode-20240101T120000-0001 - chat/completions
   🔌 [CONNECTION-CONNECTING] connectionType: grpc
   ```

2. **Language Server**:
   ```
   📡 [LS-REQUEST] vscode-20240101T120000-0001 - duo_chat/stream
   ```

3. **GitLab Rails**:
   ```
   Started POST "/api/v4/ai/duo_agent_platform/chat" correlation_id=vscode-20240101T120000-0001
   ```

4. **AI Gateway**:
   ```
   [INFO] Request received correlation_id=vscode-20240101T120000-0001 path=/v1/chat/completions
   ```

5. **Duo Workflow Service**:
   ```
   [INFO] Starting workflow correlation_id=vscode-20240101T120000-0001 workflow_id=wf_abc123
   🤖 [AGENT-EVENT] Tool selection: file_reader
   🔧 [TOOL-SELECTED] file_reader
   ⚙️ [TOOL-EXECUTING] file_reader
   ✅ [TOOL-COMPLETED] file_reader
   ```

6. **LangSmith**: Complete trace with all agent decisions and LLM calls

### Error Scenarios

Look for these patterns when debugging:

- **Connection Issues**: `❌ [CONNECTION-ERROR]` in VS Code logs
- **Authentication Problems**: `401 Unauthorized` in GitLab Rails logs
- **Agent Failures**: `❌ [TOOL-FAILED]` in Duo Workflow logs
- **LLM Errors**: Check LangSmith for model-specific errors

## 🛠️ Debugging Workflows

### 1. End-to-End Request Tracing

```bash
# Start monitoring in one terminal
./monitor-dap-traces.sh

# In VS Code Extension Development Host:
# 1. Open GitLab Duo Chat
# 2. Send a query: "What files are in this repository?"
# 3. Watch logs for correlation ID flow

# Analyze the trace
./analyze-dap-traces.sh trace <correlation_id_from_logs>
```

### 2. Agent Orchestration Analysis

Check LangSmith for:
- Agent reasoning steps
- Tool selection logic
- Context gathering process
- Planning and execution phases

### 3. Performance Analysis

```bash
# Check workflow performance
./analyze-dap-traces.sh performance

# Look for:
# - Slow tool executions
# - LLM response times
# - Network latency issues
```

## 📝 Log Levels and Verbosity

### VS Code Extension
- Set `"gitlab.debug": true` in VS Code settings
- Set `VSCODE_GITLAB_VERBOSE_LOGGING=true` environment variable

### AI Gateway
- `AIGW_LOGGING__LEVEL=debug`
- `AIGW_LOGGING__ENABLE_REQUEST_LOGGING=true`
- `AIGW_LOGGING__ENABLE_LITELLM_LOGGING=true`

### Duo Workflow Service
- `DUO_WORKFLOW_LOGGING__LEVEL=debug`
- `GRPC_TRACE=http_keepalive,call_error,connectivity_state,channel`
- `GRPC_VERBOSITY=DEBUG`

## 🔧 Troubleshooting

### Common Issues

1. **No LangSmith Traces**
   - Check API key configuration
   - Verify `LANGCHAIN_TRACING_V2=true`
   - Ensure network connectivity to LangSmith

2. **Missing Correlation IDs**
   - Restart VS Code extension
   - Check language server connection
   - Verify GitLab authentication

3. **Incomplete Traces**
   - Check service health: `gdk status`
   - Verify log file permissions
   - Restart services: `gdk restart`

### Advanced Debugging

```bash
# Check service connectivity
curl -v http://localhost:3000/api/v4/ai/duo_agent_platform/health

# Test gRPC connection
grpcurl -plaintext localhost:50052 list

# Verify OpenTelemetry endpoint
curl -v http://localhost:4318/v1/traces
```

## 📈 Next Steps

1. **Set up your LangSmith API key** for complete agent tracing
2. **Run the enhanced tracing setup** script
3. **Start monitoring** and send test queries
4. **Analyze traces** to understand the complete DAP flow
5. **Use correlation IDs** to follow requests end-to-end

For additional help, check the individual log files and use the analysis scripts provided.
